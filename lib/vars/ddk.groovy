import groovy.transform.Field

import org.swqa.tools.common
import org.swqa.tools.git

@Field def commonLib = new common()
@Field def gitLib = new git()

def genRelyMRPackageUrl(String relyRepo, String relyBranch, String packageName) {
    def ossPath = constants.genOssPath(relyRepo, relyBranch)
    def ossAlias = constants.genOssAlias(ossPath)
    def relyOssPath = "${ossAlias}/${ossPath}"
    def relyCommit = commonLib.findLatestOssPkg(relyOssPath, "*_${packageName}").split('_')[0]
    return constants.genPackageUrl(relyRepo, relyBranch, relyCommit, packageName)
}

def installUmd(String packageUrl = '', Boolean onlyFirmware = false) {
    def packageName = env.umdPackageName ?: 'x86_64-mtgpu_linux-xorg-release-hw-glvnd.tar.gz'
    def branch = env.gitlabSourceRepoName == 'gr-kmd' ? env.gitlabTargetBranch : null
    packageUrl = packageUrl ?: (env.umdPackageUrl ?: constants.genLatestPackageUrl('gr-umd', branch, packageName))
    def relyBranch = commonLib.findMrDependency('gr-umd', (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: ''))
    packageUrl = relyBranch ? genRelyMRPackageUrl('gr-umd', relyBranch, packageName) : packageUrl
    currentBuild.description += "umd: ${packageUrl}<br>"
    dir('ddk_umd') {
        constants.downloadPackage(packageUrl)
        String fullPackageName = packageUrl.split('/')[-1]
        def unpackCmd = fullPackageName.endsWith('.gz') ? 'tar -xzf' : 'tar -xf'
        sh "${unpackCmd} ${fullPackageName}"
        // def installScriptPath = sh(script: 'find . -iname install.sh', returnStdout: true).trim().split()[0]
        def firmwareFolderPath = sh(script: 'find . -type d -name firmware', returnStdout: true).trim()
        if (onlyFirmware) {
            sh """
                rm -rf /lib/firmware/musa* ||:
                rm -rf /lib/firmware/mthreads ||:
                mkdir -p /lib/firmware
                cp -rf ${firmwareFolderPath}/* /lib/firmware
                ls -l /lib/firmware ||:
                ls -l /lib/firmware/mthreads ||:
            """
        } else {
            def glvndArgs = packageUrl.contains('glvnd') ? '-g -n ' : ''
            def extraUnInstallArgs = packageUrl.contains('/stable/') ? '-u' : '-u .'
            def extraInstallArgs = packageUrl.contains('/stable/') ? '' : '-s .'
            sh """
                cd "\$(dirname "\$(find . -type f -name "install.sh" -print -quit)")"
                ./install.sh ${glvndArgs} ${extraUnInstallArgs} || true
                ./install.sh ${glvndArgs} ${extraInstallArgs}
                cp ./usr/local/etc/X11/xorg.conf /etc/X11/xorg.conf ||:
                sed -i "1i /usr/lib/`uname -m`-linux-gnu/musa" /etc/ld.so.conf.d/`uname -m`-linux-gnu.conf
                ldconfig
            """
        }
    }
}

def uninstallUmd() {
    dir('ddk_umd') {
        def glvndArgs = env.umdPackageUrl.contains('glvnd') ? '-g -n ' : ''
        def extraUnInstallArgs = env.umdPackageUrl.contains('/stable/') ? '-u' : '-u .'
        sh """
            cd "\$(dirname "\$(find . -type f -name "install.sh" -print -quit)")"
            ./install.sh ${glvndArgs} ${extraUnInstallArgs} || true
        """
    }
}

def installKmd(String packageUrl = '', String options='') {
    timeout(15) {
        def packageName = env.kmdPackageName ?: 'x86_64-mtgpu_linux-xorg-release-hw.deb'
        def branch = env.gitlabSourceRepoName == 'gr-umd' ? env.gitlabTargetBranch : null
        packageUrl = packageUrl ?: (env.kmdPackageUrl ?: constants.genLatestPackageUrl('gr-kmd', branch, packageName))
        def relyBranch = commonLib.findMrDependency('gr-kmd', (env.gitlabMergeRequestTitle ?: '') + (env.gitlabMergeRequestDescription ?: ''))
        packageUrl = relyBranch ? genRelyMRPackageUrl('gr-kmd', relyBranch, packageName) : packageUrl
        currentBuild.description += "kmd: ${packageUrl}<br>"
        dir('ddk_kmd') {
            // gpu-sched is new dependency required by mtgpu
            constants.downloadPackage(packageUrl)
            String fullPackageName = packageUrl.split('/')[-1]
            if (fullPackageName.endsWith('.tar.gz') || fullPackageName.endsWith('.tar')) {
                def unpackCmd = fullPackageName.endsWith('.gz') ? 'tar -xzf' : 'tar -xf'
                sh """${unpackCmd} ${fullPackageName}"""
                def mtgpuPath = sh(script: 'find . -name mtgpu.ko', returnStdout: true).trim()
                sh """
                    sudo rmmod mtgpu || true
                    sudo insmod /usr/lib/modules/`uname -r`/kernel/sound/soundcore.ko || true
                    sudo insmod /usr/lib/modules/`uname -r`/kernel/sound/core/snd.ko || true
                    sudo insmod /usr/lib/modules/`uname -r`/kernel/sound/core/snd-timer.ko || true
                    sudo insmod /usr/lib/modules/`uname -r`/kernel/sound/core/snd-pcm.ko || true
                    sudo insmod /usr/lib/modules/`uname -r`/kernel/sound/core/snd-pcm-dmaengine.ko || true
                    sudo modprobe drm_kms_helper
                    sudo modprobe vfio
                    sudo modprobe vfio_mdev || sudo modprobe mdev
                    sudo modprobe gpu-sched
                    sudo insmod ${mtgpuPath} ${options}
                """
            } else if (fullPackageName.endsWith('.deb')) {
                sh 'dpkg --configure -a ||:'
                sh 'dpkg -l | grep dkms || (apt update && apt install dkms -y)'
                sh 'modprobe gpu-sched || (apt update && apt install linux-modules-extra-\$(uname -r) -y && modprobe gpu-sched)'
                try {
                    sh "dpkg -i ${fullPackageName}"
                } catch (exc) {
                    sh 'cat /var/lib/dkms/mtgpu/1.0.0/build/make.log ||:'
                    throw new Exception(exc.message)
                }
                if (options) {
                    sh "echo options mtgpu ${options} > /etc/modprobe.d/mtgpu.conf"
                }
            } else if (fullPackageName.endsWith('.rpm')) {
                sh 'modprobe gpu-sched || (apt update && apt install linux-modules-extra-$(uname -r) -y && modprobe gpu-sched)'
                sh "rpm -ivh ${fullPackageName}"
                if (options) {
                    sh "echo options mtgpu ${options} > /etc/modprobe.d/mtgpu.conf"
                }
            } else {
                throw new Exception("Format not supported: ${packageName.split('\\.')[-1]}")
            }
        }
    }
// install
// check lsmod | mtgpu
}

def installVdiHost(String packageUrl = '', String options='') {
    def packageName = env.vdiHostPackageName ?: 'xc-kmd_mtgpu_amd64_5.4.0-42-generic_release.tar.gz'
    def branch = env.gitlabTargetBranch ?: 'develop'
    def latestUrl = "https://oss.mthreads.com/release-ci/VDI/XC-VDI/${branch}/drivers/latest.txt"
    def latestContent = utils.runCommandWithStdout("curl --insecure ${latestUrl}").with {
        it.endsWith('_') ? it[0..-2] : it
    }
    def latestVdiHostPackageUrl = "${latestContent}_${packageName}"
    packageUrl = packageUrl ?: latestVdiHostPackageUrl
    currentBuild.description += "VDI Host: ${packageUrl}<br>"
    dir('ddk_vdi') {
        // gpu-sched is new dependency required by mtgpu
        def vdiHostFolder = constants.downloadAndUnzipPackage(packageUrl)
        sh """
            modprobe drm_kms_helper; modprobe vfio; modprobe vfio_mdev; modprobe mdev; modprobe kvm; modprobe gpu-sched
            rm -rf /lib/firmware/mthreads/ ||:
            mkdir -p /lib/firmware/mthreads/ ||:
            cp -rf ${vdiHostFolder}/firmware/* /lib/firmware/mthreads/
            ls /lib/firmware/mthreads/
            insmod ${vdiHostFolder}/mtgpu.ko ${options}
            dmesg -T | tee dmesg.log
            grep -q 'MooreThreads GPU drm driver loaded successfully' dmesg.log
        """
    }
}

def waitForSshConnection(String sshLogin) {
    sh """
        while ! timeout 10 ${sshLogin} 'ls'; do
            sleep 5
        done
    """
}

def installVdiGuest(String packageUrl = '') {
    def sshLogin = 'sshpass -p 1 ssh -p 2201 root@localhost -o StrictHostKeyChecking=no'
    def packageName = env.vdiGuestPackageName ?: 'musa_vGPU_amd64.deb'
    def branch = env.gitlabTargetBranch ?: 'develop'
    def latestUrl = "https://oss.mthreads.com/release-ci/VDI/XC-VDI/${branch}/drivers/latest.txt"
    def latestContent = utils.runCommandWithStdout("curl --insecure ${latestUrl}").with {
        it.endsWith('_') ? it[0..-2] : it
    }
    def latestVdiGuestPackageUrl = "${latestContent}_${packageName}"
    packageUrl = packageUrl ?: latestVdiGuestPackageUrl
    currentBuild.description += "VDI Guest: ${packageUrl}<br>"
    sh '''
        cd /home/<USER>/linuxGuest
        rm -rf linux-guest-01.qcow2 ||:
        ./create-1-img.sh -b uos-backing.img
        ./start-x.sh &
    '''
    waitForSshConnection(sshLogin)
    sh "${sshLogin} 'cd vdiCI; wget ${packageUrl}; dpkg -i *${packageName}'"
    utils.catchErrorContinue { sh "${sshLogin} 'reboot'" }
    waitForSshConnection(sshLogin)
}

def installVdiAndSetup(
    String vdiHostPackageUrl=env.vdiHostPackageUrl,
    String insmodOptions=env.insmodOptions,
    String vdiGuestPackageUrl=env.vdiGuestPackageUrl
) {
    def sshLogin = 'sshpass -p 1 ssh -p 2201 root@localhost -o StrictHostKeyChecking=no'
    sh 'grep -qxF "kernel.panic_on_oops=0" /etc/sysctl.conf || echo "kernel.panic_on_oops=0" >> /etc/sysctl.conf; sysctl -p'
    commonLib.reboot(env.NODE_NAME)
    commonLib.recoverEnv()
    apt.installPackage('sshpass qemu qemu-system qemu-kvm libvirt-daemon-system libvirt-clients bridge-utils virtinst virt-manager mdevctl')
    try {
        installVdiHost(vdiHostPackageUrl, insmodOptions)
    } catch (exc) {
        sh 'dmesg -T'
        sh 'echo b > /proc/sysrq-trigger'
        throw exc
    }
    try {
        installVdiGuest(vdiGuestPackageUrl)
    } catch (exc) {
        sh 'dmesg -T'
        sh "${sshLogin} 'dmesg -T'"
        throw exc
    }
}

def installLinuxDdk(String linuxDdkPackageUrl, Boolean force = false) {
    // install ddk deb
    def branch = env.linuxDdkBranch ?: 'master'
    def packageUrl = linuxDdkPackageUrl ?: constants.genLatestPackageUrl('linux-ddk', branch, env.packageName ?: 'ddk2.0.deb')

    def packageName = packageUrl.split('/')[-1]
    dir('mt-ddk') {
        if (packageName.endsWith('tar.gz') || packageName.endsWith('tar')) {
            constants.downloadAndUnzipPackage(packageUrl)
            sh '''
                cd linux-ddk/build
                ./install.sh
            '''
        } else {
            def rpmInstallCmd = 'rpm -ivh'
            def debInstallCmd = force ? 'dpkg -i --force-all' : 'dpkg -i'
            def installCmd = packageUrl.contains('deb') ? debInstallCmd : rpmInstallCmd
            def pkgCmd = packageUrl.contains('deb') ? 'apt' : 'yum'
            constants.downloadPackage(packageUrl)
            if (env.runChoice == 'pod') {
                sh '''
                    dpkg-deb -R *.deb linux-ddk
                    cp -r linux-ddk/usr/local/musa /usr/local/
                '''
            } else {
                commonLib.runRelyNetwork {
                    sh "${pkgCmd} install libcunit1 libdrm2 -y ||:"
                // sh "${pkgCmd} install linux-modules-extra-\$(uname -r) -y ||:"
                }
                try {
                    sh """
                        mkdir -p /etc/modprobe.d ||:
                        ${installCmd} *${packageName}
                    """
                } catch (exc) {
                    sh 'cat /var/lib/dkms/mtgpu/1.0.0/build/make.log ||:'
                    throw new Exception(exc.message)
                }
            }
        }
    }
}

def uninstallLinuxDdk() {
    dir('mt-ddk/linux-ddk/build') {
        sh './install.sh -r 1'
    }
}

def driverCheck() {
    sh '''
        export LD_LIBRARY_PATH=/usr/local/musa/lib:${LD_LIBRARY_PATH}
        export PATH=/usr/local/musa/bin:${PATH}
        echo "executing muMemCpyTest & musaInfo now..."
        command -v muMemcpyTest > /dev/null 2>&1 && muMemcpyTest
        command -v musaInfo > /dev/null 2>&1 && musaInfo
    '''
}

def installLinuxDdkAndSetup(String linuxDdkPackageUrl=env.linuxDdkPackageUrl, Boolean isStartXorg=false, Closure closure=null) {
    // restart machine
    commonLib.reboot(env.NODE_NAME)
    // env recovery
    commonLib.recoverEnv()
    // install ddk deb
    installLinuxDdk(linuxDdkPackageUrl)

    // install kmd deb
    if (env.kmdPackageUrl) {
        installLinuxDdk(env.kmdPackageUrl, true)
    }

    if (closure) {
        closure.call()
    }

    if (env.modprobeOptions) {
        sh "echo '${env.modprobeOptions}' > /etc/modprobe.d/mtgpu.conf"
        sh 'cat /etc/modprobe.d/mtgpu.conf'
    }
    // restart machine
    commonLib.reboot(env.NODE_NAME)
    // modprobe mtgpu
    if (env.sleepBeforeModprobe) {
        sleep(env.sleepBeforeModprobe)
    }
    sh '''
        modprobe mtgpu; sleep 10
        modinfo mtgpu || :
        cat /sys/module/mtgpu/parameters/GeneralSVMHeapPageSize || :
    '''
    // Boolean checkDriver=true
    // checkDriver && driverCheck()
    if (isStartXorg) {
        // glvnd --> '/usr/lib/xorg/Xorg'
        // none-glvnd --> '/usr/local/bin/Xorg'
        commonLib.startXorg('/usr/lib/xorg/Xorg')
    }
}

def installMusa(String linuxMusaPackageUrl) {
    // restart machine
    commonLib.reboot(env.NODE_NAME)
    // env recovery
    commonLib.recoverEnv()
    // install musa deb
    sh """
        if [ -d dkms_pkg]; then
            rm -r dkms_pkg
        fi
        mkdir dkms_pkg
        cd dkms_pkg
        wget -q ${linuxMusaPackageUrl}
        dpkg -i *.deb
    """
    // restart machine
    commonLib.reboot(env.NODE_NAME)
    // modprobe mtgup
    sh 'modprobe mtgpu; sleep 10'
    commonLib.startXorg()
}

def installLibdrm(String branch, String commitId = '') {
    commitId = gitLib.fetchCode('libdrm-mt', branch, commitId)
    dir('libdrm-mt') {
        sh '''
            apt install -y meson ninja-build ||:
            meson builddir --prefix=/usr/local/lib/musa
            ninja -C builddir install
        '''
    }
    currentBuild.description += "libdrm: ${branch} | ${commitId}<br>"
}

def installMediaDriver(String packageUrl = '') {
    currentBuild.description += "media driver: ${packageUrl}<br>"
    constants.downloadAndUnzipPackage(packageUrl)
    sh '''
        cd mt_video && ./install.sh -u && ./install.sh -a x86_64 -t ubuntu-2.12 -v 2.20
        dpkg --force-overwrite -i libva_2.20_x86_64.deb
        dpkg --force-overwrite -i ffmpeg_4.4.2_x86_64.deb
    '''
}

def installFFmpeg(String packageUrl = '') {
    packageUrl = packageUrl ?: env.ffmpegPackageUrl
    currentBuild.description += "ffmpeg url: ${packageUrl}<br>"
    def packageName = constants.downloadPackage(packageUrl)
    sh "dpkg --force-overwrite -i ${packageName}"
}

def install(Boolean isStartXorg=false) {
    // restart machine
    commonLib.reboot(env.NODE_NAME)
    // env recovery
    commonLib.recoverEnv()
    // install umd and insmod kmd deb
    installUmd()
    installKmd()
    // restart machine
    commonLib.reboot(env.NODE_NAME)
    // modprobe mtgup
    sh 'modprobe mtgpu; sleep 10'
    if (isStartXorg) {
        commonLib.startXorg()
    }
// verify - glxinfo | grep opengl, search for: "vendor string: MOORE THREADS" & "render string: MTT"
}

def loadBuildConfig(String repo, String branch, Map binding = [:]) {
    def configName = env.configName ?: 'buildConfig.yaml'
    def configUrl = "${constants.OSS.URL_PREFIX}/${constants.OSS.BUILD_BUCKET}/${repo}/config"
    def configFile = "${configUrl}/${configName}"
    log.info("Using config file: ${configFile}")
    def configContent = sh(script: "curl --insecure ${configFile}", returnStdout: true)
    if (configContent.toLowerCase().contains('nosuchkey')) {
        throw("No config ${configName} found in ${configUrl}")
    }
    def config = readYaml text: commonLib.formatString(configContent, binding)
    // load branch config
    def branchConfigfile = "${configUrl}/${branch}/${configName}"
    def branchConfigContent = sh(script: "curl --insecure ${branchConfigfile}", returnStdout: true)
    if (branchConfigContent.toLowerCase().contains('nosuchkey')) {
        // branch confing does not exist
        println('No branch specified config found, use default...')
    } else {
        log.info("Loading branch config: ${branchConfigfile}")
        def branchConfig = readYaml text: commonLib.formatString(branchConfigContent, binding)
        if (branchConfig.disableDefaultBuildList) {
            config.buildList = []
        }
        config = commonLib.mergeMap([config, branchConfig])
    }
    return config
}

def installWinDriver(String driverUrl=null, String subPackageUrl=null) {
    driverUrl = driverUrl ?: winTest.fetchLatestDriverUrl()
    if (subPackageUrl) {
        winTest.update_driver_with_subpackage(driverUrl, subPackageUrl)
    } else {
        winTest.update_driver(driverUrl)
    }
}
