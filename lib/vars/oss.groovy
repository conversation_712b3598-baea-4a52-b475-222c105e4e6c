import groovy.transform.Field
import groovy.json.JsonSlurper

@Field OSS_CI = 'https://swci-oss.mthreads.com'
@Field OSS_CI_USER = 'mtoss-swci'
@Field OSS_CI_PWD = 'H65KwcY1poayvgqTEReHBJu'
@Field OSS = 'https://oss.mthreads.com'
@Field OSS_USER = 'mtoss-swci'
@Field OSS_PWD = 'Z2Z4LWNp'

def verify() {
    try {
        sh 'mc -v'
        return true
    } catch (exc) {
        return false
    }
}

def checkOSSAlias(String host, String alias_name, String username, String password) {
    try {
        String content = utils.runCommandWithStdout("mc alias ls ${alias_name} --json")
        def lines = content.readLines()
        def jsonLine = lines.find { it.startsWith('{') }
        println(jsonLine)
        def jsonSlurper = new JsonSlurper()
        def data = jsonSlurper.parseText(jsonLine)
        return data.status == 'success' && data.URL == host && data.accessKey == username && data.secretKey == password
    } catch (exc) {
        return false
    }
}

def checkOSSAlias(String alias_name) {
    try {
        String content = utils.runCommandWithStdout("mc alias ls ${alias_name} --json")
        def lines = content.readLines()
        def jsonLine = lines.find { it.startsWith('{') }
        println(jsonLine)
        def jsonSlurper = new JsonSlurper()
        def data = jsonSlurper.parseText(jsonLine)
        return data.status == 'success'
    } catch (exc) {
        return false
    }
}

/**
 * Sets up MinIO client aliases for both OSS endpoints
 *
 * @param username Username for the OSS endpoint
 * @param password Password for the OSS endpoint
 * @return Map containing the status of each alias setup operation
 */
def setUp(String username=OSS_USER, String password=OSS_PWD) {
    def setupAlias = { String aliasName, String endpoint, String user, String pwd ->
        def cmd = "mc alias set ${aliasName} ${endpoint} ${user} ${pwd}"
        try {
            utils.retryWithRandomBackoff(600, 10, {
                utils.runCommand(cmd)
                echo "Successfully set alias for ${aliasName}"
            })
            return true
        } catch (e) {
            utils.showErrorMessage("Failed to set alias for ${aliasName} after multiple retries: ${e.message}")
            return false
        }
    }

    def ossResult = true
    def ossciResult = true
    // Execute both commands independently
    if (!checkOSSAlias(OSS, 'oss', username, password)) {
        ossResult = setupAlias('oss', OSS, username, password)
    }
    if (!checkOSSAlias(OSS_CI, 'swci-oss', OSS_CI_USER, OSS_CI_PWD)) {
        ossciResult = setupAlias('swci-oss', OSS_CI, OSS_CI_USER, OSS_CI_PWD)
    }

    // Return a map with the status of each operation
    return [
        'oss': ossResult,
        'swci-oss': ossciResult
    ]
}

def install(String username=OSS_USER, String password=OSS_PWD) {
    try {
        if (!verify()) {
            def arch = sh(script: 'arch', returnStdout: true).trim()
            def mcUrl = constants.mcUrl[arch]
            utils.retryWithRandomBackoff(600, 10, {
                sh """
                    rm -rf /usr/local/bin/mc ||:
                    wget -q --no-check-certificate ${mcUrl} -O /usr/local/bin/mc
                    chmod 755 /usr/local/bin/mc
                """
            })
        }
        return setUp(username, password)
    } catch (exc) {
        utils.showErrorMessage("Failed to install mc: ${exc.message}")
        return false
    }
}

def cp(String src, String dest='./', recursive=true, retries=10) {
    // mc cp oss/xx//file will failed ...
    src = src.replaceAll('//+', '/')
    dest = dest.replaceAll('//+', '/')
    def res = null
    def attempt = 0
    def success = false
    def errorMessage = ''
    timeout(time: env.ossTimeout ?: 60, unit: 'MINUTES') {
        waitUntil(initialRecurrencePeriod: 30000, quiet: true) {
            attempt++
            try {
                println "cp from ${src} to ${dest}"
                // def ossAlias = dest.split('/')[0]
                // if (!checkOSSAlias(ossAlias)) {
                //     error "oss alias ${ossAlias} is not set"
                // }
                def cmd = recursive ? 'mc cp -r' : 'mc cp'
                res = utils.runCommand("${cmd} ${src} ${dest}")
                success = true
            } catch (exc) {
                utils.showErrorMessage("Attempt ${attempt} failed: ${exc.message}")
                errorMessage = exc.message
                success = false
            }
            return success || (attempt == retries)
        }
    }
    if (!success) {
        error errorMessage
    }
    return res
}

def ls(String src) {
    utils.retryWithRandomBackoff(600, 10, {
        return utils.runCommandWithStdout("mc ls ${src}")
    })
}
