import groovy.transform.Field

@Field Map jenkinsCredentials = [
    'SSH_GITLAB': [sshUserPrivateKey(credentialsId: 'sh-code-ssh', keyFileVariable: 'PKEY')],
    'API_GITLAB': [string(credentialsId: SH_CODE_API_TOKEN_NAME, variable: 'API_TOKEN_GITLAB')],
    'COV': [usernamePassword(credentialsId: '4b48533e-6b4d-4bc8-bff5-60dcb223c69c', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')],
    'JIRA': [usernamePassword(credentialsId: '2b304ecf-fd52-4dac-9f56-2aec35415248', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')],
    'DB-SWQA-CI': [usernamePassword(credentialsId: 'DB-SWQA-CI', usernameVariable: 'USERNAME', passwordVariable: 'PASSWORD')],
]

def runWithCredential(String credential='SSH_GITLAB', Closure closure) {
    withCredentials(jenkinsCredentials[credential]) {
        if (credential.contains('SSH')) {
            withEnv(["GIT_SSH_COMMAND=ssh -i $PKEY -o StrictHostKeyChecking=no"]) {
                closure.call()
            }
        } else {
            closure.call()
        }
    }
}
