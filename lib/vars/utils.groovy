import java.security.SecureRandom
import org.apache.commons.lang3.StringEscapeUtils
import groovy.json.JsonSlurper

import org.swqa.tools.common

def listFirstFolderInTarFile(String tarFile) {
    return sh(script: "tar --list -f ${tarFile} | head -n 1", returnStdout: true).replace('/', '').trim()
}

def curl(String url) {
    return sh(script: "curl ${url}", returnStdout: true).trim()
}

def retryClosure(Number seconds=60 * 10, Closure closure) {
    timeout(time: seconds, unit: 'SECONDS') {
        waitUntil {
            try {
                closure.call()
                true
            } catch (error) {
                input 'retry ?'
                false
            }
        }
    }
}

def catchErrorContinue(Closure closure) {
    try {
        closure.call()
    } catch (e) {
        echo "Error: ${e.message}. Ignoring error and continuing."
    }
}

def runCommand(String command) {
    if (isUnix()) { return sh(script: command) }
    return bat(script: command)
}

def runCommandWithStdout(String command) {
    if (isUnix()) { return sh(script: command, returnStdout: true).trim() }
    return bat(script: command, returnStdout: true).trim()
}

def runCommandWithStatus(String command) {
    if (isUnix()) { return sh(script: command, returnStatus: true) }
    return bat(script: command, returnStatus: true)
}

def installConda() {
    if (env.condaPackageUrl) {
        sh """
            wget -q ${env.condaPackageUrl}
            mkdir -p /home/<USER>
            tar -xf ${env.condaPackageUrl.split('/')[-1]} -C /home/<USER>
        """
    }else {
        print('no conda url do nothing')
    }
}

def checkJiraId() {
    log.info("gitlabMergeRequestTitle: ${env.gitlabMergeRequestTitle}")
    log.info("gitlabMergeRequestDescription: ${env.gitlabMergeRequestDescription}")
    def regex = /(?i)\b(SW|DIAGSYS|KUAE|COMPILER|ARCH|PRM|PH\d+S?|PH\d+GPU|VPS|COMPILERIN|QY|HG|AP|AS|PERF|RAT|HS|MTVDI)\b-\d+/
    if (!env.gitlabMergeRequestTitle || env.gitlabSourceRepoName == 'SWQA-CI') { return }
    if (!(StringEscapeUtils.unescapeJava(env.gitlabMergeRequestTitle) =~ regex || StringEscapeUtils.unescapeJava(env.gitlabMergeRequestDescription) =~ regex)) {
        currentBuild.description = currentBuild.description ?: ''
        currentBuild.description += 'Merge request title|description must contain a jira id.<br>'
        currentBuild.description += "Current supported regex: ${regex}<br>"
        currentBuild.description += 'Add comment CIRERUN when you fix this.<br>'
        error 'Merge request title|description must contain a jira id.'
    }
}

def checkCard() {
    try {
        def result = sh(script: 'lspci | grep 1ed5', returnStdout: true)
        println result
    } catch (exc) {
        new common().toggleNodeOffline(env.NODE_NAME, true, 'Missing card')
        throw new Exception('Missing card!')
    }
}

def showErrorMessage(message) {
    catchError(buildResult: null, stageResult: null) {
        error(message)
    }
}

def uploadDbgSym(String workDir, String ossScriptPath, String extraArgs = '') {
    oss.install()
    def scriptsName = ossScriptPath.split('/')[-1]
    def localScriptPath = "/usr/local/bin/${scriptsName}"
    sh """
        mc cp ${ossScriptPath} ${localScriptPath}
        chmod 755 ${localScriptPath}
        find ${workDir} -name "*.dbg" -exec ${scriptsName} {} ${extraArgs} \\;
        find ${workDir} -name "*.dbg" -delete
    """
}

def isEmpty(obj) {
    obj == null || obj == 'null' || obj?.isEmpty() ?: false
}

def setStatus(name, status) {
    curl("http://qa-zentao.mthreads.com:7379/SET/${name}/${status}")
}

def getStatus(name) {
    String jsonStr = curl("http://qa-zentao.mthreads.com:7379/GET/${name}")
    def status = new JsonSlurper().parseText(jsonStr)
    return status.GET
}

def getEnvString() {
    def result = ''
    for (key in env.getEnvironment().keySet()) {
        result += "${key}=${env[key]}\n"
    }
    return result
}

/**
 * Retries a function with random wait times until it succeeds or the maximum retry time is reached.
 *
 * @param maxRetryTimeSeconds Maximum time in seconds to keep retrying
 * @param maxIntervalSeconds Maximum interval in seconds between retries (actual interval will be random between 1 and this value)
 * @param closure The function to retry
 * @return The result of the function if successful, or throws the last error if all retries fail
 */
def retryWithRandomBackoff(int maxRetryTimeSeconds=600, int maxIntervalSeconds=10, Closure closure) {
    def startTime = System.currentTimeMillis()
    def endTime = startTime + (maxRetryTimeSeconds)
    def lastError = null
    def attempt = 0

    while (System.currentTimeMillis() < endTime) {
        attempt++
        try {
            def result = closure.call()
            echo "Function succeeded on attempt ${attempt}"
            return result
        } catch (e) {
            lastError = e
            def randomWaitSeconds = 1 + new SecureRandom().nextInt(maxIntervalSeconds)
            echo "Attempt ${attempt} failed: ${e.message}. Retrying in ${randomWaitSeconds} seconds..."
            sleep(randomWaitSeconds)
        }
    }

    // If we get here, all retries failed
    echo "All retry attempts failed within ${maxRetryTimeSeconds} seconds after ${attempt} attempts"
    throw lastError ?: new Exception("Function failed after ${attempt} attempts")
}

def toStringParams(Map params) {
    def strParams = [:]
    params.each { k, v ->
        strParams[k] = v instanceof CharSequence ? v : groovy.json.JsonOutput.toJson(v)
    }
    return strParams
}

def runScripts(List<String> commands, boolean failFast = true, String label = 'Custom Scripts') {
    /* groovylint-disable-next-line UnnecessaryCollectCall */
    commands = commands.collect { it.replaceAll(/^(['"])(.*)\1$/, '$2') }
    def date = new Date().format('yyyy.MM.dd')
    def isNodeUnix = isUnix()
    def tempFileName = isNodeUnix ? '.temp_script.sh' : 'temp_script.bat'
    def runCmd

    if (isNodeUnix) {
        def header = """
            #!/bin/bash
            # ========================================
            # Auto-generated shell script: ${tempFileName}
            # Label: ${label}
            # Created by Jenkins on ${date}
            # ========================================
            set -e
        """.stripIndent()
        def scriptContent = [header.trim(), commands.join('\n')].join('\n\n')
        writeFile(file: tempFileName, text: scriptContent)
        runCmd = "chmod +x ${tempFileName}; ./${tempFileName}"
        echo '📜 Script Content:'
        sh "cat ${tempFileName}"
    } else {
        def header = """
            @echo off
            REM ========================================
            REM Auto-generated batch script: ${tempFileName}
            REM Label: ${label}
            REM Created by Jenkins on ${date}
            REM ========================================
        """.stripIndent()
        def echoLines = [header.trim(), commands.join('\r\n')].join('\r\n\r\n')
        writeFile(file: tempFileName, text: echoLines)
        runCmd = tempFileName
        echo '📜 Script Content:'
        bat "type ${tempFileName}"
    }

    try {
        echo "🚀 Running: ${label}"
        runCommand(runCmd)
        echo "✅ Script completed: ${label}"
    } catch (err) {
        echo "❌ Error in script '${label}': ${err.getMessage()}"
        if (failFast) {
            error('Stopping pipeline due to script failure.')
        }
    }
}
