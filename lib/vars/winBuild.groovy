import org.swqa.tools.git

def build_wddm(String driverEnv = 'hw', String build_file, String module, String os_model = 'Win10 Release', String arch = 'x64', String buildType = 'release', String build_path = 'wddm\\build\\wddm') {
    echo "buildType in build_wddm: ${buildType}"
    def buildDir = driverEnv == 'nohw' ? "${build_path}\\nohw" : "${build_path}\\mtgpu"
    def baseEnvVars = [
        'CHCP': '65001',
        'LANG': 'en_US.UTF-8',
        'BUILD': 'rebuild',
        'BUILDTYPE': "${os_model}^|${arch}"
    ]

    echo "Initial baseEnvVars: ${baseEnvVars}"

    if (buildType.contains('release_branch')) {
        baseEnvVars['RELEASE_BRANCH'] = '1'
        echo 'RELEASE_BRANCH added to baseEnvVars.'
    } else {
        echo "Condition buildType.contains('release_branch') not met. buildType: ${buildType}"
    }

    echo "Final baseEnvVars: ${baseEnvVars}"
    def extraEnvVars = [:]
    if (env.buildBranch == 'release_vgpu_2.5.6' && module == 'mtkm') {
        extraEnvVars = [
            'SUPPORT_VZ_VGPUD': '1',
            'SUPPORT_VZ_FW_CHECK': '1',
            'RELEASE_BRANCH': '1',
            'PACKAGE_VERSION': '2.5.6-002',
            'VGPU_COMPAT_CHECK_MODE_VERSION_LIST': '1',
        ]
    }else if ((env.buildBranch == 'release_vgpu_2.5.6_001' || env.buildBranch == 'vgpu_2.5.6_for_h3c' ) && module == 'mtkm') {
        extraEnvVars = [
            'SUPPORT_VZ_VGPUD': '1',
            'SUPPORT_VZ_FW_CHECK': '1',
            'RELEASE_BRANCH': '1',
            'PACKAGE_VERSION': '2.5.6-001',
            'VGPU_COMPAT_CHECK_MODE_VERSION_LIST': '1',
        ]
    }else if ((env.vdiBranch == 'release_vgpu_2.7.0' || env.buildBranch == 'release_vgpu_2.7.0' ) && module == 'mtkm') {
        extraEnvVars = [
            'SUPPORT_VZ_FW_CHECK': '1',
            'RELEASE_BRANCH': '1',
            'PACKAGE_VERSION': '2.7.0-003',
            'VGPU_COMPAT_CHECK_MODE_VERSION_LIST': '1',
        ]
    }

    def finalEnvVars = baseEnvVars + extraEnvVars
    def batCommands = finalEnvVars.collect { key, value -> "set $key=$value" }.join('\n')

    dir(buildDir) {
        timeout(20) {
            def coverityCommand = env.coverity_tool_path ? "\"${env.coverity_tool_path}\\cov-build.exe\" --dir ${env.idir} --add-arg -D__RGXINCLUDE_H__" : ''
            bat """
                ${batCommands}
                ${coverityCommand} ${build_file} ${module}
            """
        } // timeout
    }
}

def build_directstream(String arch = 'x64') {
    String architecture = arch == 'Win32' ? 'x86' : arch

    dir('DirectStream\\build\\MTEncodeBuild') {
        bat """
            set "BUILDTYPE=Release|${architecture}"
            vs_mtencode_build.bat
        """
    }
}

def build_panel(Map ciConfig, String driverType = 'hw', boolean buildMtPanel = true, boolean buildDxcPanel = true, boolean buildOglPanel = true, String buildType = 'release') {
    def productPaths = ciConfig.product_paths
    def defaultDriverPath = formatPath("${getPath(productPaths.x64.common, buildType, productPaths.x64.common.release)}")

    if (driverType.contains('nohw')) {
        defaultDriverPath = formatPath("${getPath(productPaths.x64.nohw, buildType, productPaths.x64.nohw.release)}")
    }

    def panelBuildConfigs = [
        [enabled: buildMtPanel, buildFunc: this.&build_mtpanel, panelDir: 'wddm\\mtdxum\\tools\\mtPanel\\build\\Release', panelExe: 'mtpanel.exe', panelPdb: 'mtpanel.pdb'],
        [enabled: buildDxcPanel, buildFunc: this.&build_dxcPanel, panelDir: 'wddm\\dxc\\tool\\dxcPanel\\build\\Release', panelExe: 'dxcPanel.exe', panelPdb: 'dxcPanel.pdb'],
        [enabled: buildOglPanel, buildFunc: this.&build_oglpanel, panelDir: 'wddm\\ogl\\tools\\oglpanel\\build\\Release', panelExe: 'oglpanel.exe', panelPdb: 'oglpanel.pdb']
    ]

    panelBuildConfigs.each { config ->
        if (config.enabled) {
            config.buildFunc()
            retry(5) {
                try {
                    bat """
                        copy /Y "${config.panelDir}\\${config.panelExe}" "${defaultDriverPath}\\symbols"
                        copy /Y "${config.panelDir}\\${config.panelPdb}" "${defaultDriverPath}\\symbols"
                    """
                } catch (e) {
                    echo 'Attempt failed. Retrying in 10 seconds...'
                    sleep time: 60, unit: 'SECONDS'
                    throw e
                }
            }
        }
    }
}

def build_mtpanel() {
    dir('wddm\\mtdxum\\tools\\mtPanel') {
        bat '''
            call genSettings.bat
            cmake -S .\\ -B .\\build -A Win32
        '''
        dir('build') {
            bat 'cmake --build . --config Release'
        }
    }
}

def build_dxcPanel() {
    dir('wddm\\dxc\\tool\\dxcPanel') {
        bat '''
            cmake -S .\\ -B .\\build -A Win32
        '''

        dir('build') {
            bat 'cmake --build . --config Release'
        }
    }
}

def build_oglpanel() {
    def qtPath = 'D:\\Qt\\Qt5.9.9_win64_static'
    def qtUrl = 'https://oss.mthreads.com/dependency/QT-StaticLibrary/Qt5.9.9_win64_static.7z'
    def outputPath = "${qtPath}.7z"

    if (!fileExists(qtPath)) {
        dir('D:\\Qt') {
            try {
                bat """
                    curl -sS "${qtUrl}" --output "${outputPath}" --insecure && 7z x "${outputPath}"
                """
            } catch (e) {
                error "Error occurred while downloading or extracting the file: ${e}"
            }
        }
    }

    dir('wddm\\ogl\\tools\\oglpanel') {
        bat """
            set QTTOOLS=${qtPath}
            mkdir build
            cd build
            cmake -DSHARED_INCLUDE_PATH=../../../../shared_include ../
            cmake --build . --config Release -j 12
        """
    }
}

def build_ogl(String build_type = 'hw', String arch='x64', String additionalFlag = '') {
    arch = arch == 'Win32' ? 'x86' : arch

    def additionalFlagsMap = [
        'mtcc20': '-DBUILD_WITH_COMPILER_2_0=ON',
        'newapi': '-DBUILD_NEW_API_ENTRY=ON -DENABLE_COMPILER_DUMP=ON',
        'hw': '-DENABLE_COMPILER_DUMP=ON'
    ]

    def commonOptions = [
        '-G "Ninja"',
        '-DCMAKE_BUILD_TYPE=Release',
        '-DBUILD_TEST=OFF',
        '-DOGL_ENABLE_UNIFIED_FW=ON',
        additionalFlagsMap.get(build_type, ''),
        additionalFlag,
        '-DSHARED_INCLUDE_PATH=../../shared_include'
    ].join(' ')

    def buildCommands = [
        'x64': "cmake ${commonOptions} -DCMAKE_INSTALL_PREFIX=./ -S .. -B ./",
        'x86': "cmake ${commonOptions} -S ../ -B ./ -DCMAKE_INSTALL_PREFIX=./install -DCONFIG=wnow32"
    ]

    def cmd = buildCommands[arch]

    def coverityCommand = env.coverity_tool_path ? "\"${env.coverity_tool_path}\\cov-build.exe\" --dir ${env.idir} --add-arg -D__RGXINCLUDE_H__" : ''

    dir('wddm\\ogl') {
        bat """
            mkdir build_${arch}
            cd build_${arch}
            ${cmd}
            ${coverityCommand} cmake --build ./ -j 12
        """
    }
}

def build_glc() {
    dir('wddm\\ogl') {
        bat '''
            mkdir build_glc
            cd build_glc
            cmake -G "Ninja" -DCMAKE_BUILD_TYPE=Debug -DOGL_STANDALONE_GLC=ON -DENABLE_COMPILER_DUMP=ON -DOGL_ENABLE_UNIFIED_FW=ON -DCMAKE_INSTALL_PREFIX=./ -DBUILD_TEST=OFF ..
            cmake --build ./ --config Debug -j 12 --target glc
        '''
    }
}

def build_mtdxvaum(String arch='x64') {
    String archNumberPart = arch.replaceAll('[^\\d]', '')
    dir('wddm\\mtvideodrv\\dxva') {
        bat "python cmake_mtdxva.py -g 1 -b 1 -a ${archNumberPart}"
    }
}

def build_mtvppum(String arch='x64') {
    String archNumberPart = arch.replaceAll('[^\\d]', '')
    dir('wddm\\vpp') {
        bat "python cmake_mtvppum.py -g 1 -b 1 -a ${archNumberPart}"
    }
}

def build_mtkmd(String build_type = 'hw', String driverType = 'hw') {
    def flags
    if (driverType == 'hw_wddm32') {
        flags = '-e 0 -b 1 -wddm 2'
    } else if (env.driver_type && env.driver_type.contains('hw_wddm32')) {
        flags = '-e 0 -t 2 -b 1 -wddm 2'
    }else {
        def flagsMap = [
            hw    : '-e 0 -b 1 -wddm 1',
            vps   : '-e 1 -b 1',
            emu   : '-e 3 -b 1',
            nohw  : '-e 2 -b 1'
        ]
        flags = flagsMap[build_type] ?: error("Unsupported build_type: ${build_type}")
    }
    dir('wddm\\kmd') {
        bat "python cmake_kmd.py ${flags}"
    }
}

def build_mtvpukm() {
    dir('wddm\\mtvideodrv\\wddm') {
        bat 'python cmake_vpukm.py -g 1 -t 0 -a 64 -b 1'
    }
}

def build_mtdxum(String arch = 'x64', String chip = 'sudi', String buildType = 'release', String compilerType = 'new', String buildTool = 'cmake', String fwVersion = '2.0') {
    /*
        -a决定平台位数：64对应64位、32对应32位
        -b是布尔变量：如果为 true、True或者1，则会编译驱动，如果为0，仅仅只会构建vs工程
        -t决定编译类型：0是release版本、1是debug版本、2是release_branch版本
        -m是布尔变量：如果为 true、True或者1，则会使用new compiler编译驱动
        -e决定编译驱动运行在什么环境：0是hw、1是vps、2是pdump、3是emu
        -c决定Chip类型：0是SUDI + QY1/2, 1是PH
    */

    def buildToolMap = ['vs': '0', 'cmake': '1'] // -b
    def buildTypeMap = ['release': '0', 'debug': '1', 'release_branch': '2'] // -t
    def compilerTypeMap = ['old': '0', 'new': '1'] // -m
    def chipMap = ['sudi': 'Win', 'ph1': 'Win', 'hg': 'Win'] // -c
    def fwMap = ['1.0': '0', '2.0': '1'] // -fw

    String mappedBuildTool = buildToolMap[buildTool]
    String mappedBuildType = buildTypeMap[buildType]
    String mappedCompiler = compilerTypeMap[compilerType]
    String mappedChip = chipMap[chip]
    String mappedFwVersion = fwMap[fwVersion]

    String archNumberPart = arch.replaceAll('[^\\d]', '')

    if (mappedBuildTool == null || mappedBuildType == null || mappedCompiler == null || mappedChip == null || mappedFwVersion == null) {
        error 'Invalid input parameter for type, compilerType, or build.'
    }

    def coverityCommand = env.coverity_tool_path ? "\"${env.coverity_tool_path}\\cov-build.exe\" --dir ${env.idir} --add-arg -D__RGXINCLUDE_H__" : ''

    if (env.gitlabTargetBranch == 'windows_9.0_release' || env.buildBranch == 'windows_9.0_release') {
        dir('wddm\\mtdxum') {
            bat "${coverityCommand} python cmake_mtdxum.py -a ${archNumberPart} -b ${mappedBuildTool} -t ${mappedBuildType} -m ${mappedCompiler} -fw ${mappedFwVersion}"
        }
    } else {
        dir('wddm\\mtdxum') {
            def extraWddm = (env.driver_type && env.driver_type.contains('hw_wddm32')) ? ' -wddm 2' : ''
            bat "${coverityCommand} python cmake_mtdxum.py -a ${archNumberPart} -b ${mappedBuildTool} -t ${mappedBuildType}${extraWddm}"
        }
    }

    if (chip != 'sudi') {
        def chipPath = "wddm\\libgfxc\\${mappedChip}"
        def archivePath = 'musa_compiler_shared.tar.gz'
        def extractedFile = 'commit_id.txt'

        if (buildType == 'release_branch') {
            chipPath += '_Release'
        }

        dir(chipPath) {
            if (!fileExists(extractedFile)) {
                bat """
                    tar -xvzf ${archivePath}
                """
            } else {
                echo 'Files already extracted, skipping extraction.'
            }
        }
    }
}

def build_Vulkan(String buildType = 'release') {
    def buildTypeMap = ['release': '0', 'debug': '1', 'release_branch': '0'] // -t
    String mappedBuildType = buildTypeMap[buildType]
    def coverityCommand = env.coverity_tool_path ? "\"${env.coverity_tool_path}\\cov-build.exe\" --dir ${env.idir} --add-arg -D__RGXINCLUDE_H__" : ''
    dir('wddm\\Vulkan') {
        bat "${coverityCommand} python cmake_mtvulkan.py -t ${mappedBuildType} -a x64 -c 1 -b 1"
    }
}

def build_m3d(String m3d_path = 'wddm\\mtdxum\\imported\\m3d', String sharedIncludePath = "${env.WORKSPACE}/wddm/shared_include") {
    dir("${m3d_path}\\test") {
        def cmakeOptions = '-DM3D_UNIFIED_FW=ON'
        if (sharedIncludePath) {
            cmakeOptions += " -DSHARED_INCLUDE_PATH=${sharedIncludePath}"
        } else {
            cmakeOptions += ' -DWDDMM3DCI=1'
        }

        bat """
            cmake -S .\\ -B .\\build ${cmakeOptions} || exit 1
        """
    }

    dir("${m3d_path}\\test\\build") {
        bat 'cmake --build . --config Debug'
    }
}

def build_dxc(String buildType = 'release', String arch='x64') {
    def buildTypeMap = ['release': '0', 'debug': '1', 'release_branch': '2'] // -t
    String mappedBuildType = buildTypeMap[buildType]
    String archNumberPart = arch.replaceAll('[^\\d]', '')

    def coverityCommand = env.coverity_tool_path ? "\"${env.coverity_tool_path}\\cov-build.exe\" --dir ${env.idir} --add-arg -D__RGXINCLUDE_H__" : ''

    if (env.gitlabTargetBranch == 'windows_9.0_release' || env.buildBranch == 'windows_9.0_release') {
        dir('wddm\\dxc') {
            def build_cmd = '\"C:\\Program Files (x86)\\Microsoft Visual Studio\\2019\\Community\\Common7\\IDE\\devenv.com\" DXC.sln /build Release'

            bat """
                cmake -S .\\ -B .\\build -DUSE_WDDM_SHARED_INCLUDE=1 -DDXC_UNIFIED_FW=ON -DRELEASE_BRANCH=ON
                cd build
                ${build_cmd}
            """
        }
    }else {
        dir('wddm\\dxc') {
            def extraWddm = (env.driver_type && env.driver_type.contains('hw_wddm32')) ? ' -wddm 2' : ''
            bat """
                ${coverityCommand} python cmake_dxc.py -a ${archNumberPart} -t ${mappedBuildType} -b 1${extraWddm}
            """
        }
    }
}

def getPath(Map paths, String key, String defaultPath) {
    return paths.containsKey(key) ? paths[key] : defaultPath
}

def formatPath(String path) {
    return path.replace('/', '\\')
}

def pkgProducts(Map ciConfig, String driverType = 'hw', String buildType = 'release', List<String> excludeModuleList = [], List<String> excludeProductsList = []) {
    def productPaths = ciConfig.product_paths
    def builds = ciConfig.builds
    def driverMapping = builds[driverType]

    def sourceDir
    def targetDir
    if (driverType.contains('nohw')) {
        sourceDir = formatPath("${getPath(productPaths.x64.nohw, buildType, productPaths.x64.nohw.release)}")
        targetDir = formatPath("${getPath(productPaths.x64.nohw, buildType, productPaths.x64.nohw.release)}")
    } else {
        sourceDir = formatPath("${getPath(productPaths.x64.common, buildType, productPaths.x64.common.release)}")
        targetDir = formatPath("${getPath(productPaths.x64.common, buildType, productPaths.x64.common.release)}")
    }

    ciConfig.modules.each { platform, modules ->
        modules.each { module ->
            if (excludeModuleList.contains(module.name)) {
                echo "Skipping ${module.name} as it is in the exclude list."
                return
            }

            module.products.each { product ->
                if (excludeProductsList.contains(product)) {
                    echo "Skipping ${product} as it is in the exclude list."
                    return
                }

                def modifiedSourceDir = sourceDir
                def modifiedTargetDir = targetDir

                switch (module.name) {
                    case 'mtdxum':
                        modifiedSourceDir = formatPath("${getPath(productPaths[platform].mtdxum, buildType, productPaths[platform].mtdxum.release)}")
                        break
                    case 'dxc':
                        modifiedSourceDir = formatPath("${getPath(productPaths[platform].dxc, buildType, productPaths[platform].dxc.release)}")
                        break
                    case 'ogl':
                        modifiedSourceDir = formatPath("${getPath(productPaths[platform].ogl, buildType, productPaths[platform].ogl.release)}")
                        break
                    case 'directstream':
                        modifiedSourceDir = formatPath("${getPath(productPaths[platform].directstream, buildType, productPaths[platform].directstream.release)}")
                        break
                    case 'mtcc':
                        modifiedSourceDir = formatPath("${getPath(productPaths[platform].mtcc, buildType, productPaths[platform].mtcc.release)}")
                        break
                    case 'Vulkan':
                        modifiedSourceDir = formatPath("${getPath(productPaths[platform].Vulkan, buildType, productPaths[platform].Vulkan.release)}")
                        break
                    case 'mtdxvaum':
                        modifiedSourceDir = formatPath("${getPath(productPaths[platform].mtdxvaum, buildType, productPaths[platform].mtdxvaum.release)}")
                        break
                    case 'mtvppum':
                        modifiedSourceDir = formatPath("${getPath(productPaths[platform].mtvppum, buildType, productPaths[platform].mtvppum.release)}")
                        break
                    case 'mtvpukm':
                        modifiedSourceDir = formatPath("${getPath(productPaths[platform].mtvpukm, buildType, productPaths[platform].mtvpukm.release)}")
                        break
                    default:
                        if (driverType.contains('nohw')) {
                            modifiedSourceDir = formatPath("${getPath(productPaths[platform].nohw, buildType, productPaths[platform].nohw.release)}")
                        } else {
                            modifiedSourceDir = formatPath("${getPath(productPaths[platform].common, buildType, productPaths[platform].common.release)}")
                        }
                        break
                }

                if (product.toLowerCase().contains('pdb')) {
                    modifiedTargetDir = "${targetDir}\\symbols"
                }

                if (product.contains('mticdfbg') || product.contains('mticdpxg')) {
                    modifiedSourceDir = 'wddm\\ogl\\imported\\pre-binary-kmd\\hw'
                }else if (product.contains('mtvk64.json')) {
                    modifiedSourceDir = 'wddm\\Vulkan\\ci\\win'
                }else if (product.contains('mtkm')) {
                    modifiedSourceDir = 'wddm\\kmd\\cmake_build\\Release'
                }

                if (modifiedSourceDir != targetDir) {
                    def sourceFilePath = "${modifiedSourceDir}\\${product}"
                    if (!fileExists(sourceFilePath)) {
                        sourceFilePath = "${modifiedSourceDir}\\symbols\\${product}"
                    }
                    try {
                        bat "copy /Y \"${sourceFilePath}\" \"${modifiedTargetDir}\""
                    }catch (e) {
                        new git().setGitlabStatus("win/build_${driverType}", 'failed')
                        error "${driverType} driver build failed"
                    }
                } else {
                    echo "Skipping copy operation for ${product} as source and target are the same."
                }
            }
        }
    }

    def fw_vgpu = [
        'build_gen1_vgpu',
        'build_gen2_vgpu',
        'build_gen3_vgpu'
    ]

    fw_vgpu.each { dir ->
        bat """
            copy /y wddm\\kmd\\${dir}\\*.vz.* ${targetDir} || true
        """
    }

    if (driverType == 'nohw') {
        bat """
            copy /y wddm\\ogl\\cts\\passlist_pdump.txt  ${targetDir}\\
            copy /y wddm\\ogl\\cts\\passlist-oglTests.txt  ${targetDir}\\
        """
    }else if (driverType == 'hw' && buildType == 'release') {
        def mtlogs = [
            'build_gen1',
            'build_gen2',
            'build_gen2_1',
            'build_gen3',
            'build_gen6'
        ]

        mtlogs.each { dir ->
            def targetPath = "${targetDir}\\symbols\\mtlog\\${dir}"
            bat """
                mkdir ${targetPath} 2>nul
                copy /y wddm\\kmd\\${dir}\\mtlog.dict ${targetPath}
            """
        }
    }

    def targetDirName = targetDir.split('\\\\').last()
    driverMapping.pkgNames[0].drop(1).each { type ->
        dir("${type}_driver") {
            bat "md bin\\${targetDirName}"
            bat "xcopy /E /I /Y ${env.WORKSPACE}\\${targetDir} bin\\${targetDirName}"
        }
    }
}

def upload_m3d(String repo, String branch, String commitId, String m3dPath = '') {
    def defaultDriverPath = 'wddm\\mtdxum\\imported\\m3d\\test'
    if (env.gitlabTargetBranch == 'windows_9.0_release' || env.buildBranch == 'windows_9.0_release') {
        defaultDriverPath = 'wddm\\m3d_lib\\m3d\\test'
    }
    m3dPath = m3dPath ?: defaultDriverPath
    def shortGitSha = commitId.take(9)
    def package_name = "${shortGitSha}_m3dTest.tar.gz"
    def ossBranchPath = constants.genOssPath(repo, branch, shortGitSha)
    def prefix = constants.genOssPrefix(ossBranchPath)
    oss.setUp()

    def ossAlias = constants.genOssAlias(ossBranchPath)
    dir(m3dPath) {
        bat """
            copy run_m3d_list.bat .\\build
            copy run_m3d_list_hgvps.bat .\\build
            copy run_m3d_list_ph1vps.bat .\\build || exit 1
            tar -cvzf ${package_name}  build
            mc cp ${package_name}  ${ossAlias}/${ossBranchPath}/
        """
    }
    currentBuild.description += "Driver binary: ${prefix}/${ossBranchPath}/${package_name}<br>"
}

def upload_directstream(String repo, String branch, String commitId, String directstreamPath = '') {
    def defaultDriverPath = 'DirectStream'
    directstreamPath = directstreamPath ?: defaultDriverPath
    def shortGitSha = commitId.take(9)
    def package_name = "${shortGitSha}_directstreamTest.tar.gz"
    def ossBranchPath = constants.genOssPath(repo, branch, shortGitSha)
    def prefix = constants.genOssPrefix(ossBranchPath)
    oss.setUp()

    def ossAlias = constants.genOssAlias(ossBranchPath)
    dir(directstreamPath) {
        bat """
            md test\\x64
            copy /Y build\\MTEncodeBuild\\x64\\Release\\*.exe test\\x64
            copy /Y build\\MTEncodeBuild\\Release\\*.exe test\\
            tar -cvzf ${package_name}  test
            mc cp ${package_name}  ${ossAlias}/${ossBranchPath}/
        """
    }
    currentBuild.description += "Driver binary: ${prefix}/${ossBranchPath}/${package_name}<br>"
}

def signature(Map ciConfig, String driverType = 'hw', String version, String driverPath = '', int index = 0, String infPath = 'wddm\\build\\wddm\\mtgpu\\INFs_30', String buildType = 'release', String vram = null) {
    def builds = ciConfig.builds
    def productPaths = ciConfig.product_paths
    def driverMapping = builds[driverType]
    def infName = driverMapping.infFiles[0][index]

    def defaultDriverPath = formatPath("${getPath(productPaths.x64.common, buildType, productPaths.x64.common.release)}")
    if (driverType.contains('nohw')) {
        defaultDriverPath = formatPath("${getPath(productPaths.x64.nohw, buildType, productPaths.x64.nohw.release)}")
    }

    if (driverType.contains('nohw')) {
        infPath = 'wddm\\build\\wddm\\nohw'
    }else if (driverType.contains('hw_wddm32')) {
        infPath = 'wddm\\build\\wddm\\mtgpu\\INFs_32'
    }else if (env.gitlabTargetBranch == 'windows_9.0_release' || env.buildBranch == 'windows_9.0_release') {
        infPath = 'wddm\\build\\wddm\\mtgpu\\INFs'
    }
    driverPath = driverPath ?: defaultDriverPath

    def osParam = driverType.contains('arm') ? '10_NI_ARM64' : '10_x64'

    bat """
        copy /Y "${WORKSPACE}\\${infPath}\\${infName}.inf" "${driverPath}\\"
        sed -i "s|DriverVer\\s*=\\s*[0-9]\\{1,2\\}/[0-9]\\{1,2\\}/[0-9]\\{4\\},|DriverVer = ${version},|g" "${driverPath}\\${infName}.inf"
        for %%f in (${driverPath}\\*.dll) do (
            if "%%~nxf" neq "mtdxconv64.dll" if "%%~nxf" neq "mtdxconv32.dll" (
                signtool.exe sign /a /ph /fd sha256 "%%f"
            )
        )
        signtool.exe sign /a /ph /fd sha256 "${driverPath}\\*.sys"
        inf2cat.exe /uselocaltime /os:${osParam} /driver:${driverPath}
        signtool.exe sign /a /ph /fd sha256 "${driverPath}\\*.cat"
    """

    if (vram != null) {
        bat """
            sed -i "s|FF,FF,FF,FF,FF,FF,FF,FF|00,00,00,${vram},00,00,00,00|g" "${driverPath}\\${infName}.inf"
        """
    }
}

def upload(Map ciConfig, String repo, String branch, String commitId, String driverType = 'hw', String driverPath = '', int index = 0, String buildType = 'release', String fileExtension = '.tar.gz') {
    def builds = ciConfig.builds
    def productPaths = ciConfig.product_paths
    def driverMapping = builds[driverType]
    def shortGitSha = commitId.take(9)
    def package_name = "${shortGitSha}_" + driverMapping.pkgNames[0][index] + fileExtension

    def defaultDriverPath = formatPath("${getPath(productPaths.x64.common, buildType, productPaths.x64.common.release)}")
    if (driverType.contains('nohw')) {
        defaultDriverPath = formatPath("${getPath(productPaths.x64.nohw, buildType, productPaths.x64.nohw.release)}")
    }

    driverPath = driverPath ?: defaultDriverPath
    def ossBranchPath = constants.genOssPath(repo, branch, shortGitSha)
    def prefix = constants.genOssPrefix(ossBranchPath)
    oss.setUp()

    def ossAlias = constants.genOssAlias(ossBranchPath)
    bat """
        tar -cvzf ${package_name} -C ${driverPath} *
        mc cp ${package_name} ${ossAlias}/${ossBranchPath}/
    """

    currentBuild.description += "Driver binary: ${prefix}/${ossBranchPath}/${package_name}<br>"
}

def updateLatestTxt(String ossPath, String latestFileName = 'latest.txt') {
    def basePath = ossPath.substring(0, ossPath.lastIndexOf('/'))
    def latestPath = "https://oss.mthreads.com/${ossPath}"

    if (isUnix()) {
        oss.install()
        def filePath = "${env.WORKSPACE}/${latestFileName}"
        sh """
          echo ${latestPath} > ${filePath}
          echo "Contents of ${filePath}:"
          cat ${filePath}
          mc cp ${filePath} oss/${basePath}/
        """
    } else {
        oss.setUp()
        def filePath = "%WORKSPACE%\\\\${latestFileName}"
        bat """
          echo ${latestPath} > ${filePath}
          echo "Contents of ${filePath}:"
          type ${filePath}
          mc cp ${filePath} oss/${basePath}/
        """
    }
}

def symuploader(Map ciConfig, String driverType = 'hw', String buildType = 'release') {
    def symUploaderUrl = 'https://oss.mthreads.com/release-ci/ci_tool/symuploader.exe'
    def symUploaderPath = 'D:\\win_test'
    def uploadUrl = 'https://sh-symstore.mthreads.com/upload/'
    dir(symUploaderPath) {
        bat "wget -q ${symUploaderUrl} --no-check-certificate"
    }

    def productPaths = ciConfig.product_paths
    def targetDir = driverType.contains('nohw') ?
        formatPath("${getPath(productPaths.x64.nohw, buildType, productPaths.x64.nohw.release)}") :
        formatPath("${getPath(productPaths.x64.common, buildType, productPaths.x64.common.release)}")

    bat "${symUploaderPath}\\symuploader.exe -d ${targetDir} -u ${uploadUrl} -p release"
}

def integratedBuildProcess(Map ciConfig, List<String> tasksToRemove, String driverEnv = 'hw', String driverType = 'hw', String chipType = 'sudi', String buildType = 'release', String osModel = 'Win10 Release') {
    def buildModules = ciConfig.modules
    def builds = ciConfig.builds
    echo "Build Modules: ${buildModules}"
    echo "Builds: ${builds}"
    echo "Builds: ${tasksToRemove}"

    def driverMapping = builds[driverType]?.get(0)
    if (!driverMapping) {
        echo "Driver ${driverType} is not found in the mappings."
        return
    }

    def buildScript = driverMapping.buildScript
    echo "Build Script: ${buildScript}"

    def taskLocks = [
        'dxc': "build_dxc_${env.BUILD_NUMBER}",
        'ogl': "build_ogl_${env.BUILD_NUMBER}",
        'directstream': "build_directstream_${env.BUILD_NUMBER}",
        'mtdxum': "build_mtdxum_${env.BUILD_NUMBER}",
        'Vulkan': "build_Vulkan_${env.BUILD_NUMBER}",
        'm3d': "build_mtdxum_${env.BUILD_NUMBER}",
        'wddm': "build_wddm_${env.BUILD_NUMBER}",
        'panel': "build_panel_${env.BUILD_NUMBER}"
    ]

    def parallelTasks = [:]

    buildModules.each { arch, moduleList ->
        def filteredModules = moduleList.findAll { module ->
            !(module.name in tasksToRemove || module.name == 'mtcc' || (!driverType.contains('nohw') && module.name == 'pdump'))
        }

        if (env.buildM3d == 'true' && arch == 'x64') {
            filteredModules.addAll([[name: 'm3d']])
        }
        if (env.buildPanel == 'true' && arch == 'x64') {
            filteredModules.addAll([[name: 'panel']])
        }

        filteredModules.each { module ->
            def buildTaskName = "${module.name}_${arch}"
            if ((env.buildBranch == 'release_vgpu_2.5.6' || env.buildBranch == 'release_vgpu_2.5.6_001' || env.buildBranch == 'vgpu_2.5.6_for_h3c') && module.name == 'mtdxum') { taskLocks.remove('mtdxum') }
            def taskLock = taskLocks[module.name] ?: taskLocks['wddm']

            parallelTasks[buildTaskName] = {
                lock(resource: taskLock) {
                    stage("Building ${buildTaskName}") {
                        try {
                            if ((env.buildBranch == 'release_vgpu_2.5.6' || env.buildBranch == 'release_vgpu_2.5.6_001' || env.buildBranch == 'vgpu_2.5.6_for_h3c') && module.name == 'mtdxum') {
                                build_wddm(driverEnv, buildScript, module.name, osModel, arch, buildType)
                            } else if ((env.buildBranch == 'release_vgpu_2.5.6' || env.buildBranch == 'release_vgpu_2.5.6_001' || env.buildBranch == 'vgpu_2.5.6_for_h3c' || env.gitlabTargetBranch == 'windows_9.0_release' || env.buildBranch == 'windows_9.0_release') && module.name == 'm3d') {
                                build_m3d('wddm\\m3d_lib\\m3d', '')
                            } else if (driverType == 'hw_wddm32' && module.name == 'ogl') {
                                build_ogl('mtcc20', arch)
                            } else if ( driverType == 'hw_wddm32_release' && module.name == 'ogl') {
                                build_ogl('mtcc20', arch, '-DRELEASE_BRANCH=ON -DENABLE_COMPILER_DUMP=OFF')
                            } else {
                                switch (module.name) {
                                    case 'dxc':
                                        sleep 100
                                        build_dxc(buildType, arch)
                                        break
                                    case 'ogl':
                                        build_ogl(driverEnv, arch)
                                        break
                                    case 'directstream':
                                        build_directstream(arch)
                                        break
                                    case 'mtdxum':
                                        build_mtdxum(arch, chipType, buildType)
                                        break
                                    case 'm3d':
                                        build_m3d()
                                        break
                                    case 'Vulkan':
                                        build_Vulkan(buildType)
                                        break
                                    case 'panel':
                                        build_panel(ciConfig)
                                        break
                                    case 'mtdxvaum':
                                        build_mtdxvaum(arch)
                                        break
                                    case 'mtvppum':
                                        build_mtvppum(arch)
                                        break
                                    case 'mtvpukm':
                                        build_mtvpukm()
                                        break
                                    case 'mtkm':
                                        build_mtkmd(driverEnv, driverType)
                                        break
                                    default:
                                        build_wddm(driverEnv, buildScript, module.name, osModel, arch, buildType)
                                }
                            }
                        } catch (e) {
                            echo "Error occurred while running build: ${osModel}, ${arch}, ${module.name}"
                            log.error(e.getMessage(), e)
                        }
                    }
                }
            }
        }
    }

    def cistatus = env.coverity_tool_path ? "win/build_${driverType}_coverity" : "win/build_${driverType}"
    try {
        new git().setGitlabStatus(cistatus, 'pending')

        if (env.coverity_tool_path) {
            parallelTasks.each { taskName, taskClosure ->
                echo "Running ${taskName}..."
                taskClosure()
            }
        } else {
            timeout(time: 60, unit: 'MINUTES') {
                parallel parallelTasks
            }
        }

        new git().setGitlabStatus(cistatus, 'success')
    } catch (e) {
        currentBuild.result = 'FAILURE'
        echo "Error: ${e.message}"
        new git().setGitlabStatus(cistatus, 'failed')
        error "${cistatus} driver build failed"
    }
}

def fetchMtapi(String driverPath, String mtapi_url = 'https://oss.mthreads.com/release-ci/mtapi/release_0.2/MTAPI_bbd98b6a1.zip') {
    def mtapi_pkg_name = mtapi_url.split('/')[-1]

    dir('mtapi') {
        bat """
            wget -q "${mtapi_url}" --no-check-certificate
            unzip -o "${mtapi_pkg_name}" || exit 1
        """
    }

    def dllFiles = [
        'mtapi\\WINDOWS\\Win64\\Release\\lib\\mtapi64.dll',
        'mtapi\\WINDOWS\\Win32\\Release\\lib\\mtapi32.dll'
    ]

    dllFiles.each { dllFile ->
        def targetPath = "${WORKSPACE}\\${driverPath}\\"
        bat "copy /Y \"${dllFile}\" \"${targetPath}\""
    }

    bat """
        copy /Y ${WORKSPACE}\\wddm\\build\\wddm\\mtgpu\\output\\mt_kmd\\intermediates\\fre_win10_amd64\\rgx_firmware_vgpu\\rgxfirmware_t0.elf ${WORKSPACE}\\${driverPath}\\symbols\\ || true
        copy /Y ${WORKSPACE}\\wddm\\build\\wddm\\mtgpu\\output\\mt_kmd\\intermediates\\fre_win10_amd64\\mtfw_firmware_vgpu\\mtfirmware_t0.elf ${WORKSPACE}\\${driverPath}\\symbols\\ || true
    """
}

def fetchMtml(String driverPath, String mmtmlUrl = null) {
    def latest_url_mtml = 'https://oss.mthreads.com/release-ci/management/windows/release_2.0/latest.txt'
    def mtml_url = mmtmlUrl ?: sh(script: "curl --insecure ${latest_url_mtml}", returnStdout: true).trim()
    def mtml_pkg_name = mtml_url.split('/')[-1]

    dir('mtml') {
        bat """
            wget -q "${mtml_url}" --no-check-certificate
            tar xvzf ${mtml_pkg_name}
        """
    }

    def dllFiles = [
        'mtml\\mtml.dll'
    ]

    dllFiles.each { dllFile ->
        def targetPath = "${WORKSPACE}\\${driverPath}\\"
        bat "copy /Y \"${dllFile}\" \"${targetPath}\""
    }
}

def extendDriver(Map ciConfig, String driverType = 'hw', String version) {
    def builds = ciConfig.builds
    def driverMapping = builds[driverType]
    int index = 1

    def pkgNames = driverMapping.pkgNames[0].drop(1)
    if (!pkgNames) {
        echo 'No valid types after dropping the first element. Skipping.'
    }else {
        pkgNames.each { type ->
            try {
                def driverPath = "${type}_driver\\bin\\fre_win10_amd64"

                if (type.contains('vdi')) {
                    fetchMtapi(driverPath)
                }

                if (type.contains('mttrace')) {
                    dir('wddm\\mtdxum') {
                        bat '''
                            python cmake_mtdxum.py -a 64 -b 1 -t 0 -e 1 -d 1
                            python cmake_mtdxum.py -a 32 -b 1 -t 0 -e 1 -d 1
                        '''
                    }

                    dir('wddm\\dxc') {
                        bat '''
                            python cmake_dxc.py -a 64 -b 1 -t 0 -d 1
                            python cmake_dxc.py -a 32 -b 1 -t 0 -d 1
                        '''
                    }

                    def files = [
                        [name: 'mtdxum64', basePath: 'wddm\\mtdxum', build64: 'cmake_build', build32: 'cmake_build32'],
                        [name: 'mtdxum32', basePath: 'wddm\\mtdxum', build64: 'cmake_build', build32: 'cmake_build32'],
                        [name: 'mtdxc64', basePath: 'wddm\\dxc', build64: 'build', build32: 'build32'],
                        [name: 'mtdh64', basePath: 'wddm\\dxc', build64: 'build', build32: 'build32'],
                        [name: 'mtdxc32', basePath: 'wddm\\dxc', build64: 'build', build32: 'build32'],
                        [name: 'mtdh32', basePath: 'wddm\\dxc', build64: 'build', build32: 'build32']
                    ]

                    files.each { file ->
                        def buildPath = file.name.endsWith('64') ? file.build64 : file.build32
                        def sourcePath = "${file.basePath}\\${buildPath}\\Release\\${file.name}"

                        bat """
                            rm -rf ${driverPath}\\symbols\\${file.name}.pdb
                            rm -rf ${driverPath}\\${file.name}.dll
                            cp -rf ${sourcePath}.pdb ${driverPath}\\symbols\\
                            cp -rf ${sourcePath}.dll ${driverPath}\\
                        """
                    }
                }

                if (type.contains('mtcc20')) {
                    build_ogl('mtcc20', 'x64')
                    build_ogl('mtcc20', 'x86')

                    def files = [
                        [name: 'mticdg64', arch: 'x64'], [name: 'mticdfbg64', arch: 'x64'],
                        [name: 'mticdpxg64', arch: 'x64'], [name: 'mticdg32', arch: 'x86'],
                        [name: 'mticdfbg32', arch: 'x86'], [name: 'mticdpxg32', arch: 'x86']
                    ]

                    files.each { file ->
                        def buildPath = "build_${file.arch}"
                        def sourcePath = "wddm\\ogl\\${buildPath}\\Release\\${file.name}"

                        if (file.name.contains('mticdfbg') || file.name.contains('mticdpxg')) {
                            sourcePath = "wddm\\ogl\\imported\\pre-binary-kmd\\hw\\${file.name}"
                        }

                        bat """
                            rm -rf ${driverPath}\\symbols\\${file.name}.pdb
                            rm -rf ${driverPath}\\${file.name}.dll
                            cp -rf ${sourcePath}.pdb ${driverPath}\\symbols\\
                            cp -rf ${sourcePath}.dll ${driverPath}\\
                        """
                    }
                }

                signature(ciConfig, env.driver_type, version, driverPath, index)

                upload(ciConfig, env.repo, env.gitlabTargetBranch, env.gitlabMergeRequestIid, env.driver_type, driverPath, index)
            } catch (e) {
                echo "Error processing ${type} with index ${index}: ${e.message}"
            } finally {
                index++
            }
        }
    }

    //upload m3d test
    if (env.buildM3d == 'true') {
        upload_m3d(env.repo, env.gitlabTargetBranch, env.gitlabMergeRequestIid)
    }
    //upload directstream test
    if (env.buildDirectstream == 'true') {
        upload_directstream(env.repo, env.gitlabTargetBranch, env.gitlabMergeRequestIid)
    }
}

def modifyFile(String directoryPath, String fileName, String oldValue, String newValue) {
    dir(directoryPath) {
        bat """
            sed -i "s|${oldValue}|${newValue}|g" ${fileName}
        """
    }
}

def getCommitHistory(String repo, String commitId, String ossBranchPath, String outputFile = 'commit_msg_history.txt') {
    try {
        def prefix = constants.genOssPrefix(ossBranchPath)
        def ossAlias = constants.genOssAlias(ossBranchPath)
        def basePath = ossBranchPath.substring(0, ossBranchPath.lastIndexOf('/'))
        def baseUrl = "${prefix}/${basePath}"
        def ossUrl = "${baseUrl}/${outputFile}"
        sh """
            wget -q "${ossUrl}" --no-check-certificate || echo "No existing history file"
        """

        dir(repo) {
            def commitMsg = sh(
                script: """
                    git log -1 --pretty=format:"Commit: %h%nAuthor: %an%nDate: %ad%nMessage: %s%nChanged files:" ${commitId}
                    git log -1 --name-status ${commitId}
                """,
                returnStdout: true
            ).trim()

            def timestamp = new Date().format('yyyy-MM-dd HH:mm:ss')
            def contentToAppend = """
            ==================== ${timestamp} ====================
            ${commitMsg}
            ================================================

            """

            if (!fileExists(outputFile)) {
                writeFile file: outputFile, text: contentToAppend
            } else {
                writeFile file: outputFile, text: contentToAppend, append: true
            }

            oss.setUp()

            sh """
                mc cp ${outputFile} ${ossAlias}/${basePath}/
            """

            echo "Commit history written to ${outputFile}"
            echo commitMsg
            echo '===================='

            return commitMsg
        }
    } catch (e) {
        echo "Error writing commit history: ${e.message}"
    }
}
