import org.swqa.tools.git
import groovy.json.JsonSlurper

def fetchTestRepo(String repoName, String branch = 'master', String commitId = null) {
    print 'run fetchTestRepo'
    retry(2) {
        try {
            if (!fileExists(repoName)) {
                new git().fetchCode(repoName, branch, commitId, [preBuildMerge: false, disableSubmodules: true, updateBuildDescription: true])
            } else {
                credentials.runWithCredential('SSH_GITLAB') {
                    dir(repoName) {
                        bat """
                            git reset --hard
                            git clean -dffx
                            git fetch --prune --tags --force origin
                            git checkout ${branch}
                            git branch --set-upstream-to=origin/${branch} || echo "Tracking already set"
                            git pull --rebase
                            ${commitId ? "git checkout -f ${commitId}" : ''}
                        """
                    }
                }
            }
        } catch (e) {
            print(e)
            bat "rm -rf ${repoName}"
            error 'fetch code error'
        }
    }

    if (fileExists("${repoName}/.gitmodules") && repoName != 'd3dtests') {
        new git().updateSubmodule(repoName)
    } else {
        echo "No submodules found in ${repoName}"
    }
}

def fetchwinapitrace(String os_bits = 'x64') {
    def apitrace_pkg_name = os_bits == 'x86' ? 'apitrace-latest-win32' : 'apitrace-latest-win64'
    def apitrace_tool_url = "https://swci-oss.mthreads.com/dependency/swci/apitrace/${apitrace_pkg_name}.7z"

    bat """
        rm -rf apitrace*
        wget -q ${apitrace_tool_url} || exit 1
        7z x ${apitrace_pkg_name}.7z || exit 1
        rm -rf ${apitrace_pkg_name}.7z
        move ${apitrace_pkg_name} apitrace
    """
}

def delete_oem_inf() {
    try {
        bat '''
            C:\\Windows\\System32\\pnputil.exe /enum-drivers>C:\\Test\\inf.log
        '''
        pnp_test_result = bat(script: 'cat C:\\Test\\inf.log', returnStdout: true).trim()
        pnp_test_result = pnp_test_result.replaceAll(/\s/, '')
        //echo "${pnp_test_result}"
        regs = /:oem\d+.inf[^\x00-\xff]{4}:(mt[-]+\w*[-]*\w*[-]*\w*|mtgpu_gl_hw|wddm_driver).inf[^\x00-\xff]{6}:MooreThreads/
        //echo "${regs}"
        if (pnp_test_result =~ 'OriginalName') {
            regs = /:oem\d+.inf\w{12}:(mt[-]+\w*[-]*\w*[-]*\w*|mtgpu_gl_hw|wddm_driver).inf\w{12}:MooreThreads/
        }
        def oem_list = []
        def oem_result = pnp_test_result =~ regs
        while (oem_result.find()) {
            //echo "${oem_result.group()}"
            def oem_test = (oem_result.group() =~ /oem\d+.inf/)[0]
            def oem_inf = oem_test.toString()
            oem_list.add(oem_inf)
            echo "${oem_inf}"
        }
        oem_result = null
        oem_test = null

        if (!oem_list.isEmpty()) {
            oem_list.each { oem_file ->
                echo "delete ${oem_file}"
                bat """
                    C:\\Windows\\System32\\pnputil.exe /delete-driver ${oem_file} /uninstall
                    sleep 10
                """
            }
        }
        else {
            echo 'there is no oem inf'
        }
    }
    catch (e) {
        print(e)
    }
}

def deleteLogFiles(logFiles) {
    logFiles.each { logFile ->
        if (fileExists(logFile)) {
            def files = findFiles(glob: logFile)
            if (files.size() > 0) {
                if (files[0].directory) {
                    deleteDir()
                } else {
                    bat "del /F /Q ${logFile}"
                }
            } else {
                echo "No files found for ${logFile}"
            }
        }
    }
}

def fetchLatestDriverUrl(driverType='hw', branch='develop') {
    def driverReady = false
    def latest_url_wddm = "https://oss.mthreads.com/sw-build/wddm/${branch}/latest.txt"
    def maxRetries = 120
    def waitTime = 5

    def download_driver_url = null
    timeout(time: maxRetries * waitTime, unit: 'MINUTES') {
        while (!driverReady) {
            def wddm_pkg_url = ''
            try {
                wddm_pkg_url = sh(script: "curl --insecure ${latest_url_wddm}", returnStdout: true).trim()
            } catch (e) {
                echo "curl https failed: ${e.message}"
                def http_url = latest_url_wddm.replaceFirst('https://', 'http://')
                try {
                    wddm_pkg_url = sh(script: "curl --insecure ${http_url}", returnStdout: true).trim()
                    echo "Fetched with http: ${http_url}"
                } catch (ex) {
                    echo "curl http failed: ${ex.message}"
                    wddm_pkg_url = ''
                }
            }
            if (!wddm_pkg_url) {
                println 'Failed to fetch latest_new.txt, waiting...'
                sleep time: waitTime, unit: 'MINUTES'
                continue
            }

            driverReady = true

            def wddm_pkg_name = "wddm_${driverType}.tar.gz"
            download_driver_url = "${wddm_pkg_url}_${wddm_pkg_name}"

            def checkUrlCommand = "curl -k --silent --head --fail ${download_driver_url}"
            def urlExists = sh(script: checkUrlCommand, returnStatus: true) == 0

            print(urlExists)

            if (!urlExists) {
                driverReady = false
            }

            if (!driverReady) {
                println 'One or more URLs do not exist yet. Waiting...'
                sleep time: waitTime, unit: 'MINUTES'
            }
        }
    }

    println 'All URLs exist. Proceeding with downloading.'
    return download_driver_url
}

def update_driver_latest(String driverType = 'hw', String inf_file='MT-GEN1-ENCODE') {
    echo "[update_driver_latest] inf_file=${inf_file}"
    def driver_url = fetchLatestDriverUrl(driverType)
    echo "[update_driver_latest] driver_url=${driver_url}"
    update_driver(driver_url, inf_file)
}

def update_driver(String driver_url, String inf_file='MT-GEN1-ENCODE', String pcie_id='PCI\\VEN_1ED5', boolean disableTDR = true) {
    timeout(5) {
        stage('delete oem inf') {
            delete_oem_inf()
        }
    }
    timeout(10) {
        dir('driver') {
            def pkg_name = driver_url.split('/')[-1]
            bat """
                wget -q ${driver_url} -O ${pkg_name} --no-check-certificate
                tar xvzf ${pkg_name}
            """

            def infFiles = findFiles(glob: '*.inf')
            if (infFiles.size() == 0) {
                error 'No .inf file found in driver directory!'
            }
            def inf_file_name = infFiles[0].name
            stage('update driver') {
                if (disableTDR) {
                    bat '''
                        reg add HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers /v TdrLevel /t REG_DWORD /d 0 /f
                        sleep 2
                        reg query HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers /v TdrLevel
                    '''
                }
                bat """
                    devcon update ${inf_file_name} ${pcie_id}
                    sleep 15
                    devcon rescan
                """
            }
        }
    }
}

def update_driver_with_subpackage(String driver_url, String subPackageUrl, String inf_file='MT-GEN1-ENCODE', String pcie_id='PCI\\VEN_1ED5', boolean disableTDR = true) {
    timeout(5) {
        stage('delete oem inf') {
            delete_oem_inf()
        }
    }
    timeout(10) {
        dir('driver') {
            constants.downloadAndUnzipPackage(subPackageUrl, 'subpackage')
            def pkg_name = driver_url.split('/')[-1]
            bat """
                wget -q ${driver_url} -O ${pkg_name} --no-check-certificate
                tar xvzf ${pkg_name}
                ls -l ||:
                ls -l subpackage ||:
                xcopy /Y subpackage\\* .
            """
            stage('update driver') {
                if (disableTDR) {
                    bat '''
                        reg add HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers /v TdrLevel /t REG_DWORD /d 0 /f
                        sleep 2
                        reg query HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\GraphicsDrivers /v TdrLevel
                    '''
                }
                bat """
                    devcon update ${inf_file}.inf ${pcie_id}
                    sleep 15
                    devcon rescan
                """
            }
        }
    }
}

def downloadAndExtractD3dTest(String testCommitId, String architecture, String downloadPath = 'C:\\Test') {
    String d3dTestUrl = "https://swci-oss.mthreads.com/dependency/swci/d3d/${testCommitId}_d3dtest_${architecture}.tar.gz"
    String wgetCmd = "wget -q ${d3dTestUrl} -O ${testCommitId}_d3dtest_${architecture}.tar.gz --no-check-certificate"

    dir(downloadPath) {
        bat """
            ${wgetCmd}
            tar -xzf ${testCommitId}_d3dtest_${architecture}.tar.gz
        """
    }

    if (!fileExists('D:\\')) {
        bat """
            powershell -Command "\$exePath = '${downloadPath}\\d3dtest.exe'; \$keyPath = 'HKCU:\\Software\\Microsoft\\DirectX\\UserGpuPreferences'; Set-ItemProperty -Path \$keyPath -Name \$exePath -Value 'GpuPreference=2;' -Force"
        """
    }
}

def run_d3d_test(String case_list, String testCommitId, String goldenDir, int case_num, String threads_num, String architecture) {
    // Preparation
    def testDirs = ['C:\\win_test', 'D:\\win_test']
    String testDir = testDirs[1]
    if (!fileExists('D:\\')) {
        testDir = testDirs[0]
    }

    dir(testDir) {
        fetchTestRepo('d3dtests', 'master', testCommitId)
    }

    downloadAndExtractD3dTest(testCommitId, architecture)

    dir("${testDir}\\d3dtests\\function") {
        def log_files = ['tmp', 'd3d_test.log']
        deleteLogFiles(log_files)

        if (case_num != -1) {
            def totalLines = bat(script: "wc -l ${case_list}", returnStdout: true).trim().split()[0].toInteger()
            def linesToSkip = totalLines - case_num
            bat(script: "more +${linesToSkip} ${case_list} > file.txt.new && move /y file.txt.new ${case_list}", returnStatus: true)
        }

        def test_cmd = "python get_m3d_test_results.py C:\\Test\\ d3dtest.exe ${case_list} tmp ${goldenDir}"
        if (case_list.contains('ph1')) {
            test_cmd += ' platform=PH1 threads=1'
        }
        if (case_list.contains('hg')) {
            test_cmd += ' platform=HG threads=1'
        }

        bat(script: """${test_cmd} | tee result.log""", returnStdout: false)
        def d3d_test_result = readFile('result.log').trim()
        echo "Test output:\n${d3d_test_result}"

        if (d3d_test_result.contains('pass rate:      100.000000%')) {
            echo "d3d test (${architecture}) passed!"
        } else {
            error("d3d test (${architecture}) failed!")
        }
    }
}

def d3d_test(String case_list, String testCommitId = env.d3dtest, String goldenDir = 'golden', int case_num = -1, String threads_num = '', String architecture = 'all') {
    def architectures

    if (case_list.contains('vps') || case_list.contains('dx12')) {
        architectures = ['x64']
    } else if (architecture == 'all') {
        architectures = ['x64', 'x86']
    } else {
        architectures = [architecture]
    }

    architectures.each { arch ->
        run_d3d_test(case_list, testCommitId, goldenDir, case_num, threads_num, arch)
    }
}

def d3d_async_test(String case_list) {
    dir('driver\\symbols') {
        bat 'start mtpanel.exe -DispatchUseGfxCompute=0 -InternalCdmUseGfxCompute=0'
    }
    d3d_test(case_list)
}

def waitForUrlAvailability(String url) {
    def maxRetries = 120
    def waitTime = 5

    timeout(time: maxRetries * waitTime, unit: 'MINUTES') {
        while (true) {
            def checkUrlCommand = "curl -k --silent --head --fail ${url}"
            def urlExists = sh(script: checkUrlCommand, returnStatus: true) == 0

            if (urlExists) {
                println 'URL exists. Proceeding with downloading.'
                break
            }

            println 'URL does not exist yet. Waiting...'
            sleep time: waitTime, unit: 'MINUTES'
        }
    }
}

def m3d_test(String runbat='run_m3d_list.bat', String m3d_test_url = env.m3d_test_url, String workpath='build', int wait_time=30) {
    timeout(wait_time) {
        def log_files = ['m3d_test.log']
        def pkg_name = m3d_test_url.split('/')[-1]

        waitForUrlAvailability(m3d_test_url)

        bat """
            wget -q ${m3d_test_url} -O ${pkg_name} --no-check-certificate
            tar xvzf ${pkg_name}
        """
        dir(workpath) {
            deleteLogFiles(log_files)
            bat"""
                ${runbat} >> m3d_test.log
            """
            log = bat(script: 'cat m3d_test.log', returnStdout: true).trim()
            echo log
            case_num = bat(script: "cat ${runbat} | grep exe |wc -l", returnStdout: true).trim().split('\n')[-1]
            print(case_num)
            passed_num = bat(script: 'cat m3d_test.log | grep "test pass" | wc -l', returnStdout: true).trim().split('\n')[-1]
            print(passed_num)
            if (case_num != passed_num) {
                error '1'
            }
        }
    }
}

def sdk_test(String case_List_File, String testCommitId = env.d3dtest, String goldenDir = 'golden', String os_bits = 'x64') {
    String testDir = case_List_File.contains('vps') ? 'C:\\win_test' : 'D:\\win_test'
    dir(testDir) {
        fetchTestRepo('d3dtests', 'master', testCommitId)

        fetchwinapitrace(os_bits)
    }

    def caseConfig = [
        'sdk_cases_list.txt'          : ['traceDir': 'D:\\Dx9SdkTrace\\', 'logFile': 'Dx9Sdk_test.log'],
        'dx10sdk_cases_list.txt'      : ['traceDir': 'D:\\Dx10SdkTrace\\', 'logFile': 'Dx10Sdk_test.log'],
        'dx10sdk_cases_list_gpuva.txt': ['traceDir': 'D:\\gpuvaDx10SdkTrace\\', 'logFile': 'gpuvaDx10Sdk_test.log'],
        'dx11sdk_cases_list.txt'      : ['traceDir': 'D:\\gpuvaDx11SdkTrace\\', 'logFile': 'gpuvaDx11Sdk_test.log']
    ]

    def log_files = ['Dx9Sdk_test.log', 'Dx10Sdk_test.log', 'gpuvaDx10Sdk_test.log', 'gpuvaDx11Sdk_test.log']

    def gametest_path = "${testDir}\\d3dtests\\dxsdk"
    def py_script = "${gametest_path}\\run_dx9_sdk_trace.py"
    def case_file = "${gametest_path}\\${case_List_File}"
    def result_dir = "${gametest_path}\\tmp_result\\"
    def golden_dir = "${gametest_path}\\${goldenDir}\\"

    dir(gametest_path) {
        deleteLogFiles(log_files)
    }

    def trace_dir = caseConfig.get(case_List_File)?.traceDir
    def log_path = caseConfig.get(case_List_File)?.logFile ? "${gametest_path}\\${caseConfig[case_List_File].logFile}" : null

    def test_cmd = "python ${py_script} ${case_file} ${trace_dir} ${result_dir} ${golden_dir}"

    dir("${testDir}\\apitrace\\bin") {
        bat "${test_cmd} >> ${log_path}"
    }
    def test_result = bat(script: "type ${log_path}", returnStdout: true).trim()

    echo "${test_result}"

    if (test_result.contains('pass rate:      100.000000%')) {
        echo 'SDK test passed!'
    } else {
        error("SDK test failed. Check log: ${log_path}")
    }

    dir(gametest_path) {
        deleteLogFiles(log_files)
    }
}

def dx9Sdk_test(String case_List_File, String testCommitId = env.d3dtest) {
    sdk_test(case_List_File, testCommitId)
}

def dx10Sdk_test(String case_List_File, String testCommitId = env.d3dtest) {
    sdk_test(case_List_File, testCommitId)
}

def dx11Sdk_test(String case_List_File, String testCommitId = env.d3dtest) {
    sdk_test(case_List_File, testCommitId)
}

def apitrace_test(String case_list, String testCommitId = env.d3dtest, String single_dir='D:\\single', String threads_num = '4', String architecture = 'all') {
    def architectures = ['x64', 'x86']

    if (architecture == 'all') {
        architectures.each { arch ->
            run_apitrace_test(case_list, testCommitId, single_dir, threads_num, arch)
        }
    } else {
        run_apitrace_test(case_list, testCommitId, single_dir, threads_num, architecture)
    }
}

def run_apitrace_test(String case_list, String testCommitId, String single_dir, String threads_num, String os_bits) {
    String testDir = case_list.contains('vps') ? 'C:\\win_test' : 'D:\\win_test'

    dir(testDir) {
        fetchTestRepo('d3dtests', 'master', testCommitId)

        fetchwinapitrace(os_bits)
    }

    def log_files = ['tmp_result', 'l2l_test.log', 'l2l_continue_test.log', 'l2l_fail_test.log', 'test_case_fail.txt']

    dir("${testDir}\\d3dtests\\gametrace\\") {
        deleteLogFiles(log_files)
    }

    dir("${testDir}\\apitrace\\bin") {
        def gametest_path = "${testDir}\\d3dtests\\gametrace"
        def py_script = "${gametest_path}\\run_trace_test.py"
        def case_file = "${gametest_path}\\${case_list}"
        def result_dir = "${gametest_path}\\tmp_result\\"
        def golden_dir = "${gametest_path}\\golden\\"
        def log_path = "${gametest_path}\\l2l_test.log"

        def test_cmd = "python ${py_script} ${case_file} ${single_dir} ${result_dir} ${golden_dir}"
        if (threads_num != '') {
            test_cmd += " threads=${threads_num}"
        }

        bat"""
            mkdir ${result_dir}
            ${test_cmd} |tee ${log_path}
        """

        if (check_apitrace_result(log_path, case_file, gametest_path)) {
            rerun_failed_tests(py_script, single_dir, result_dir, golden_dir, gametest_path)
        } else {
            echo 'apitrace test pass!!'
        }
    }
}

def check_apitrace_result(String log_path, String case_file, String gametest_path) {
    def test_result = bat(script: "type ${log_path}", returnStdout: true).trim()
    echo "${test_result}"

    if (test_result.contains('pass rate:      100.000000%')) {
        return false
    /* groovylint-disable-next-line UnnecessaryElseStatement */
    } else {
        collect_failed_cases(test_result, case_file, gametest_path)
        return true
    }
}

def collect_failed_cases(String test_result, String case_file, String gametest_path) {
    def list_cases = bat(script: "type ${case_file}", returnStdout: true).trim().split('\n')
    def fail_case_list = []

    test_result.eachMatch(/\w{1}[\w.' ]+trace[^\n\r]+fail/) { fail_case ->
        def fail_case_name = (fail_case =~ /\w{1}[\w. ]+trace/)[0]
        list_cases.each { test_case ->
            if (test_case.contains(fail_case_name)) {
                fail_case_list.add(test_case)
            }
        }
}

    fail_case_list.each { case_fail ->
        def cleaned_case = case_fail.replace('\r', '').replace('\n', '')
        bat "echo ${cleaned_case} >> ${gametest_path}\\test_case_fail.txt"
    }
}

def rerun_failed_tests(String py_script, String single_dir, String result_dir, String golden_dir, String gametest_path) {
    def log_path = "${gametest_path}\\l2l_fail_test.log"
    def case_file = "${gametest_path}\\test_case_fail.txt"
    def test_cmd = "python ${py_script} ${case_file} ${single_dir} ${result_dir} ${golden_dir} threads=1"

    bat"""
        mkdir ${result_dir}
        ${test_cmd} >> ${log_path}
    """

    if (check_apitrace_result(log_path, case_file, gametest_path)) {
        error('apitrace test fail')
    } else {
        echo('apitrace test pass')
    }
}

def ogl_renderdoc_test(String caselist) {
    renderdoc_test(caselist)
}

def renderdoc_test_dxc(String caselist) {
    renderdoc_test(caselist)
}

def renderdoc_test(String caselist, String goldenDir='golden', String d3dTestCommitId = env.d3dtest, String oglTestCommitId = env.ogltest) {
    def testDir
    def case_file
    def baseDir = caselist.contains('vps') ? 'C:\\win_test' : 'D:\\win_test'

    if (caselist.contains('passlist')) {
        testDir = "${baseDir}\\ogltests\\renderdoc"
        case_file = "${baseDir}\\ogltests\\passlists\\${caselist}"
        dir(baseDir) {
            fetchTestRepo('ogltests', 'master', oglTestCommitId)
        }
    } else {
        testDir = "${baseDir}\\d3dtests\\gametrace\\renderdoc"
        case_file = "${testDir}\\${caselist}"

        dir(baseDir) {
            fetchTestRepo('d3dtests', 'master', d3dTestCommitId)
        }
    }

    def log_files = ['result.log']

    deleteLogFiles(log_files)

    def py_script = "${testDir}\\replay_rdc.py"
    def result_dir = "${testDir}\\tmp_result"
    def golden_dir = "${testDir}\\${goldenDir}"
    def single_dir = 'D:\\rdcs'
    def test_cmd = "C:\\Python\\Python38\\python.exe ${py_script} ${case_file} ${single_dir} ${result_dir} ${golden_dir}"
    def bin_path = 'C:\\renderdoc_release\\Release'

    checkRes(single_dir, case_file, 'rdc')

    bat """
        set PYTHONUNBUFFERED=1
        set PYTHONPATH=${bin_path}
        ${test_cmd} | tee result.log
    """

    def testOutput = readFile('result.log').trim()
    echo 'Test Results:'
    echo testOutput

    if (testOutput.contains('pass rate:      100.000000%')) {
        echo 'Renderdoc test passed successfully'
        } else {
        error 'Renderdoc test failed'
    }
}

def vulkan_renderdoc_test() {
    def baseDir = 'D:\\win_test'

    dir(baseDir) {
        fetchTestRepo('vktests', 'master')
    }

    dir("${baseDir}\\vktests\\renderdoc") {
        def output = bat(script: 'run.bat', returnStdout: true).trim()
        echo "${output}"

        if (checkPassRate(output)) {
            echo 'Test passed!'
        } else {
            error 'Test failed! Expected output not found.'
        }
    }
}

def run_whlk_Geometry_test(String architecture) {
    def whlk_dir = 'WGF11 Geometry Shader (WoW64)'
    if (architecture == 'x86') {
        whlk_dir = 'WGF11 Geometry Shader'
    }
    dir("D:\\${whlk_dir}") {
        def out = bat(script:'WGF11GeometryShader.exe -whql -logclean -textlog -logAll -progress', returnStdout:true).trim().split(']')[-1]
        println out
        if ( out =~ 'Fail: 0' ) {
            println 'Test passed'
        }else {
            error('WHQL Geometry test failure~!')
        }
    }
}

def whlk_Geometry_test(String architecture = 'all') {
    def architectures = ['x64', 'x86']

    if (architecture == 'all') {
        architectures.each { arch ->
            run_whlk_Geometry_test(arch)
        }
    } else {
        run_whlk_Geometry_test(architecture)
    }
}

def yuanshenTrace_test() {
    String testDir = 'D:\\win_test'

    dir(testDir) {
        fetchwinapitrace('x64')
    }

    def traceFilePath = 'D:\\yuanshen\\YuanShen-new.trace'
    def expectedOutput = 'Rendered 18840 frames'

    dir("${testDir}\\apitrace\\bin") {
        def output = bat(script: "d3dretrace.exe ${traceFilePath}", returnStdout: true).trim()
        echo "${output}"

        if (output.contains(expectedOutput)) {
            echo 'Test passed!'
        } else {
            error 'Test failed! Expected output not found.'
        }
    }
}

def whlk_test(String testcaseFilename, int maxNum = 15, String testCommitId = env.whlktest) {
    String testDir = 'C:\\win_test'
    String repoDir = "${testDir}\\whlktest"
    String logPath = "${repoDir}\\dxlogs"

    if (env.threads && env.threads.isInteger() && env.threads.toInteger() > 0) {
        maxNum = env.threads.toInteger()
    }

    dir(testDir) {
        fetchTestRepo('whlktest', 'master', testCommitId)
    }

    if (!fileExists(logPath)) {
        bat "mkdir ${logPath}"
    }

    if (testcaseFilename.contains('vps')) {
        def filesToCopy = ['mtdxum64.dll', 'mtdxconv64.dll', 'mtgfxc64.dll', 'mtdxc64.dll', 'mtdh64.dll']
        echo "'vps' test detected, setting maxNum to 1."
        maxNum = 1
        dir('driver') {
            filesToCopy.each { file ->
                bat "copy /Y ${file} ${repoDir}\\x64"
            }
        }
    }

    dir(repoDir) {
        try {
            bat """
                python whlk_test.py -f "${logPath}" -c "${testcaseFilename}" -m ${maxNum}
            """
        } catch (e) {
            echo 'WHLK test failed, retrying...'
            bat """
                python splitcsv.py ${testcaseFilename} 1
                del /s /q ${logPath}\\*
                python whlk_test.py -f "${logPath}" -c ${testcaseFilename.replace('.csv', '_1.csv')} -m 1
            """
        }
    }
}

def appDecode_test() {
    def appPlayPath = 'D:\\win_test\\app_check\\App_Play'
    def resultPath = "${appPlayPath}\\result"
    def resultFile = "${resultPath}\\DecodeSimpleResult.txt"

    try {
        println("Starting AppPlayRun.bat in ${appPlayPath}")
        dir(appPlayPath) {
            bat """
                set SymbolPath=${env.WORKSPACE}\\driver\\symbols
                call AppPlayRun.bat
            """
        }

        if (!fileExists(resultFile)) {
            error("Result file ${resultFile} not found.")
        }

        dir(resultPath) {
            def output = readFile('DecodeSimpleResult.txt').trim()
            println("Decoded output: ${output}")

            if (output.contains('fail')) {
                error("Decoding test failed: 'Failed' found in DecodeSimpleResult.txt")
            }
        }

        println('AppDecode test completed successfully.')
    } catch (e) {
        println("Error during AppDecode test: ${e.message}")
        throw e
    }
}

def manageTraceCodec(toolDir, codecDir, codecUrl) {
    if (fileExists(codecDir)) {
        echo "Directory ${codecDir} exists, cleaning up TraceLog."
        dir(codecDir) {
            bat '''
                rm -rf TraceLog || true
            '''
        }
    } else {
        echo "Directory ${codecDir} does not exist, downloading and extracting trace-codec."
        dir(codecDir) {
            bat """
                wget -q ${codecUrl} -O trace-codec.zip --no-check-certificate
                unzip trace-codec.zip -d ${toolDir}
                rm -rf trace-codec.zip
            """
        }
    }
}

def startTraceCodec(codecDir) {
    echo 'Starting trace codec.'
    dir(codecDir) {
        bat '''
            StartTrace.bat
        '''
    }
}

def stopTraceCodec(codecDir, symbolsDir) {
    echo 'Stopping trace codec.'
    dir(codecDir) {
        bat """
            StopTrace.bat ${symbolsDir}
        """
    }
}

def directstream_test() {
    def toolDir = 'D:\\win_test'
    def codecDir = "${toolDir}\\trace-codec"
    def codecUrl = 'https://swci-oss.mthreads.com/dependency/directstream/trace-codec.zip'
    def symbolsDir = "${env.WORKSPACE}\\driver\\symbols"
    def enc_test = 'success'

    try {
        def directstream_pkg_name = env.directstream_test_url.split('/')[-1]

        bat """
            wget -q ${env.directstream_test_url} -O ${directstream_pkg_name}
            tar xvzf ${directstream_pkg_name}
        """

        manageTraceCodec(toolDir, codecDir, codecUrl)
        startTraceCodec(codecDir)

        bat '''
            xcopy "test" "test_x64" /E /I /Y
            xcopy "test\\x64\\*" "test_x64" /E /I /Y
        '''

        parallel(
            '64-bit Test': {
                dir('test_x64') {
                    bat """
                        copy /Y ${env.WORKSPACE}\\driver\\mtencodeapi64.dll .
                    """
                    run_directstream_test()
                }
            },
            '32-bit Test': {
                dir('test') {
                    bat """
                        copy /Y ${env.WORKSPACE}\\driver\\mtencodeapi32.dll .
                    """
                    run_directstream_test()
                }
            }
        )
    } catch (e) {
        enc_test = 'failed'
        error "Error during test execution: ${e.message}"
    } finally {
        stopTraceCodec(codecDir, symbolsDir)

        if (enc_test == 'failed') {
            def driverUrl = env.driver_url ?: env.driverUrl
            def enclogs = driverUrl.split('/')[-1].replace('.tar.gz', '_tracking_log.tar.gz')
            def pathStartIndex = driverUrl.indexOf('://') + 3
            def pathEndIndex = driverUrl.lastIndexOf('/')
            def oss_save_path = driverUrl.substring(pathStartIndex, pathEndIndex)

            def delimiter = 'swci-oss.mthreads.com/'
            if (oss_save_path.contains(delimiter)) {
                oss_save_path = oss_save_path.substring(oss_save_path.indexOf(delimiter) + delimiter.length())
            }

            println "enclogs: $enclogs"
            println "oss_save_path: $oss_save_path"

            oss.setUp()

            bat """
                md tracking_log
                move /Y test\\x64\\mtencode_log.txt tracking_log\\mtencode_log_x64.txt
                move /Y test\\mtencode_log.txt tracking_log\\mtencode_log_x86.txt
                copy /Y ${codecDir}\\TraceLog\\*.log tracking_log\\
                tar -cvzf ${enclogs} tracking_log
                mc cp ${enclogs} oss/${oss_save_path}/
            """

            currentBuild.description += "Tracking log: https://swci-oss.mthreads.com/${oss_save_path}/${enclogs} <br>"
        }
    }
}

def run_directstream_test() {
    bat '''
        enc_ci.sh
        cat log.txt
    '''

    def result = sh(script: 'cat result.txt', returnStdout: true).trim()
    if (result.toInteger() == 1) {
        error 'Test failed in result.txt!'
    }
}

def getVideoSourceDir(codec) {
    def baseDir = (bat(script: 'dir D:\\', returnStatus: true) == 0) ? 'D:\\videos' : 'C:\\videos'
    return "${baseDir}\\${codec}"
}

def prepareTestEnvironment(codec, decode_resources) {
    try {
        def sourceDir = getVideoSourceDir(codec.codec)
        for (def textFile in codec.textFiles) {
            echo "${decode_resources}${textFile}"
        }

        dir(sourceDir) {
            for (def textFile in codec.textFiles) {
                if (textFile.toLowerCase().endsWith('.txt')) {
                    bat "wget -q ${decode_resources}${textFile} -O ${sourceDir}\\${textFile} --no-check-certificate"
                } else if (!fileExists(textFile)) {
                    bat "wget -q ${decode_resources}${textFile} -O ${sourceDir}\\${textFile} --no-check-certificate"
                }
            }
            if (!fileExists(codec.zipFile)) {
                bat "wget -q ${decode_resources}${codec.zipFile} -O ${sourceDir}\\${codec.zipFile} --no-check-certificate"
                bat "7z x ${codec.zipFile}"
            }
            for (def textFile in codec.textFiles) {
                bat "cat ${textFile}"
            }
        }
    } catch (e) {
        print(e)
        bat "rm -rf ${getVideoSourceDir(codec.codec)}"
        throw e
    }
}

def dxva_dx11_test(String os_bits = 'x64') {
    def codecs = [
        [name: 'VC1', codec: 'VC1', textFiles: ['VC1ConformanceTestList.txt'], zipFile: 'VC1.zip'],
        [name: 'H264', codec: 'H264', textFiles: ['StrmListLongSliceMode1.txt'], zipFile: 'H264.zip'],
        [name: 'H265', codec: 'H265', textFiles: ['HevcConfromanceTestListSuccessed.txt', 'Hevc10bWindows.txt'], zipFile: 'H265.zip'],
        [name: 'VP9', codec: 'VP9', textFiles: ['VP9ConformanceTestList.txt'], zipFile: 'VP9.zip'],
        [name: 'VP9_10BIT', codec: 'VP9_10bit', textFiles: ['VP910bitConformanceTestList.txt'], zipFile: 'VP910bit.zip'],
        [name: 'AV1_8BIT', codec: 'AV1_8bit', textFiles: ['av1_conformanceTestList8bit.txt'], zipFile: 'av18bit.zip'],
        [name: 'AV1_10BIT', codec: 'AV1_10bit', textFiles: ['av1_conformanceTestList10bit.txt'], zipFile: 'av110bit.zip']
    ]
    def decode_resources = 'https://swci-oss.mthreads.com/dependency/swci/dxva/'
    def dxva_test_dir = "${env.WORKSPACE}\\dxvascripts"

    fetchTestRepo('dxvascripts', 'master')
    dir('dxvascripts') {
        bat """
            wget -q ${decode_resources}ffmpeg-av1.zip -O ffmpeg-av1.zip --no-check-certificate
            unzip -o ffmpeg-av1.zip
            cd exe
            unzip ffmpeg.zip
        """
    }

    codecs.each { codec ->
        prepareTestEnvironment(codec, decode_resources)
    }

    def dxva_tasks = [:]

    dir(dxva_test_dir) {
        bat 'rm -f All_ResultFail.txt'
        def ffmpegExe = ".\\exe\\ffmpeg_${os_bits}.exe"
        def ffmpegExe_av1 = '.\\ffmpeg-av1\\bin\\ffmpeg.exe'
        def sourceDirs = ['C:\\videos', 'D:\\videos']
        String sourceDir = (bat(script: 'dir D:\\', returnStatus: true) == 0) ? sourceDirs[1] : sourceDirs[0]

        def isCDrive = sourceDir.toUpperCase().startsWith('C:')

        dxva_tasks['h264'] = {
            stage('h264') {
                def h264VideoSourceDir = "${sourceDir}\\H264"
                bat"""
                    python .\\run_regression.py -s ${ffmpegExe} -i ${h264VideoSourceDir}\\StrmListLongSliceMode1.txt -p ${sourceDir} -f yuv420p -o ${h264VideoSourceDir}\\DX11_H264_out -r 2 -n 2
                    cat ResultFail.txt > All_ResultFail.txt
                    rm -rf ${h264VideoSourceDir}\\DX11_H264_out
                    python .\\run_regression.py -s ${ffmpegExe}  -a d3d11va -i .\\lists\\264_single.txt -p ${sourceDir} -f yuv420p -o ${h264VideoSourceDir}\\DX11_H264_out -r 2 -n 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    rm -rf ${h264VideoSourceDir}\\DX11_H264_out
                    python .\\run_regression.py -s ${ffmpegExe}  -a d3d11va -i .\\lists\\265_single.txt -p ${sourceDir} -f yuv420p -o ${h264VideoSourceDir}\\DX11_H264_out -r 2 -n 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    cat All_ResultFail.txt
                    rm -rf ${h264VideoSourceDir}\\DX11_H264_out
                """
            }
        }

        dxva_tasks['h265'] = {
            stage('h265') {
                def h265VideoSourceDir  = "${sourceDir}\\H265"
                bat"""
                    python .\\run_regression.py -s ${ffmpegExe} -i ${h265VideoSourceDir}\\HevcConfromanceTestListSuccessed.txt -p ${sourceDir} -f yuv420p -o ${h265VideoSourceDir}\\DX11_H265_out -r 2 -n 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    rm -rf ${h265VideoSourceDir}\\DX11_H265_out
                    python .\\run_regression.py -s ${ffmpegExe} -a d3d11va -i ${h265VideoSourceDir}\\Hevc10bWindows.txt -p ${sourceDir} -f p010le -o ${h265VideoSourceDir}\\DX11_H265_out -r 2 -n 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    rm -rf ${h265VideoSourceDir}\\DX11_H265_out
                    python .\\run_regression.py -s ${ffmpegExe} -i ${h265VideoSourceDir}\\Hevc10bWindows.txt -p ${sourceDir} -f p010le -o ${h265VideoSourceDir}\\DX11_H265_out -r 2 -n 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    cat All_ResultFail.txt
                    rm -rf ${h265VideoSourceDir}\\DX11_H265_out
                """
            }
        }

        dxva_tasks['vp9'] = {
            stage('vp9') {
                def vp9VideoSourceDir = "${sourceDir}\\VP9"
                def vp910bitVideoSourceDir = "${sourceDir}\\VP9_10bit"
                bat"""
                    python .\\run_regression.py -s ${ffmpegExe} -i ${vp9VideoSourceDir}\\VP9ConformanceTestList.txt -p ${sourceDir} -f nv12 -o ${vp9VideoSourceDir}\\DX11_VP9_out -r 2 -n 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    rm -rf ${vp9VideoSourceDir}\\DX11_VP9_out
                    python .\\run_regression.py -s ${ffmpegExe} -a d3d11va -i .\\lists\\VP9_single.txt -p ${sourceDir} -f nv12 -o ${vp9VideoSourceDir}\\DX11_VP9_out -r 2 -n 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    rm -rf ${vp9VideoSourceDir}\\DX11_VP9_out
                """
                if (!isCDrive) {
                    bat"""
                        python .\\run_regression.py -s ${ffmpegExe} -i ${vp910bitVideoSourceDir}\\VP910bitConformanceTestList.txt -p ${sourceDir} -f yuv420p10le -o ${vp910bitVideoSourceDir}\\DX11_VP9_10bit_out -r 2 -n 2 || exit 1
                        cat ResultFail.txt >> All_ResultFail.txt
                        cat All_ResultFail.txt
                        rm -rf ${vp910bitVideoSourceDir}\\DX11_VP9_10bit_out
                    """
                } else {
                    echo 'Skip VP9 10bit test on C drive.'
                }
            }
        }

        dxva_tasks['AV1'] = {
            stage('AV1') {
                def av18bitVideoSourceDir = "${sourceDir}\\AV1_8bit"
                def av110bitVideoSourceDir = "${sourceDir}\\AV1_10bit"
                if (!isCDrive) {
                    bat"""
                        python .\\run_regression.py -s ${ffmpegExe_av1} -i ${av18bitVideoSourceDir}\\av1_conformanceTestList8bit.txt -p ${sourceDir} -f yuv420p -o ${av18bitVideoSourceDir}\\DX11_AV1_8bit_out -r 2
                        cat ResultFail.txt >> All_ResultFail.txt
                        rm -rf ${av18bitVideoSourceDir}\\DX11_AV1_8bit_out
                        python .\\run_regression.py -s ${ffmpegExe_av1} -i ${av110bitVideoSourceDir}\\av1_conformanceTestList10bit.txt -p ${sourceDir} -f yuv420p10le -o ${av110bitVideoSourceDir}\\DX11_AV1_10bit_out -r 2
                        cat ResultFail.txt >> All_ResultFail.txt
                        cat All_ResultFail.txt
                        rm -rf ${av110bitVideoSourceDir}\\DX11_AV1_10bit_out
                    """
                } else {
                    echo 'Skip AV1 10bit test on C drive.'
                }
            }
        }

        dxva_tasks['VC1'] = {
            stage('VC1') {
                def vc1VideoSourceDir = "${sourceDir}\\VC1"
                bat"""
                    python .\\run_regression.py -s ${ffmpegExe} -i ${vc1VideoSourceDir}\\VC1ConformanceTestList.txt -p ${sourceDir} -f yuv420p -o ${vc1VideoSourceDir}\\DX11_VC1_out -r 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    cat All_ResultFail.txt
                    rm -rf ${vc1VideoSourceDir}\\DX11_VC1_out
                """
            }
        }

        if (isCDrive) {
            dxva_tasks.remove('VC1')
            dxva_tasks.each { name, taskClosure ->
                taskClosure.call()
            }
        } else {
            parallel dxva_tasks
        }

        def out = bat(script:'cat All_ResultFail.txt', returnStdout: true).trim()
        println '--------------'
        println out
        println out.length()
        if ( out.contains('H264') || out.contains('H265') || out.contains('vp9') || out.contains('av1') || out.contains('vc1')) {
            println 'some cases failed-----'
            print(out)
            error('error,please check ')
        }else {
            println 'Test passed'
        }
    }
}

def dxva_dx12_test(String os_bits = 'x64') {
    def codecs = [
        [name: 'VC1', codec: 'VC1', textFiles: ['DX12-VC1ConformanceTestList.txt'], zipFile: 'VC1.zip'],
        [name: 'H264', codec: 'H264', textFiles: ['StrmListLongSliceMode1.txt'], zipFile: 'H264.zip'],
        [name: 'H264_filed', codec: 'H264_filed', textFiles: ['DX12-H264fieldConformanceTestList.txt'], zipFile: 'filed_streams_h264.zip'],
        [name: 'H265', codec: 'H265', textFiles: ['DX12-HevcConfromanceTestList8bit.txt', 'DX12-HevcConfromanceTestList10bit.txt'], zipFile: 'H265.zip'],
        [name: 'VP9', codec: 'VP9', textFiles: ['DX12-VP9ConformanceTestList8bit.txt'], zipFile: 'VP9.zip'],
        [name: 'VP9_10BIT', codec: 'VP9_10bit', textFiles: ['DX12-VP9ConformanceTestList10bit.txt'], zipFile: 'VP910bit.zip'],
        [name: 'AV1_8BIT', codec: 'AV1_8bit', textFiles: ['DX12-AV1ConformanceTestList8bit.txt'], zipFile: 'av18bit.zip'],
        [name: 'AV1_10BIT', codec: 'AV1_10bit', textFiles: ['DX12-AV1ConformanceTestList10bit.txt'], zipFile: 'av110bit.zip']
    ]

    def dxva_test_dir = "${env.WORKSPACE}\\dxvascripts"
    def decode_resources = 'https://swci-oss.mthreads.com/dependency/swci/dxva/'
    def ffmpegExe = '.\\exe\\ffmpeg-2024-05-02-git-71669f2ad5-full_build\\bin\\ffmpeg.exe'

    fetchTestRepo('dxvascripts', 'master')
    dir('dxvascripts\\exe') {
        bat '''
            7z x ffmpeg-2024-05-02-git-71669f2ad5-full_build.7z -y
        '''
    }

    def dxva_tasks = [:]

    dir(dxva_test_dir) {
        codecs.each { codec ->
            prepareTestEnvironment(codec, decode_resources)
        }

        bat 'rm -f All_ResultFail.txt'
        def sourceDirs = ['C:\\videos', 'D:\\videos']
        String sourceDir = (bat(script: 'dir D:\\', returnStatus: true) == 0) ? sourceDirs[1] : sourceDirs[0]

        dxva_tasks['H264 DX12'] = {
            stage('H264') {
                def h264VideoSourceDir = "${sourceDir}\\H264"
                bat"""
                    python .\\run_regression.py -s ${ffmpegExe}  -a d3d12va -i ${h264VideoSourceDir}\\StrmListLongSliceMode1.txt -p ${sourceDir} -f yuv420p -o ${h264VideoSourceDir}\\DX12_H264_out -r 2 -n 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    cat All_ResultFail.txt
                    rm -rf ${h264VideoSourceDir}\\DX12_H264_out
                """
            }
        }

        dxva_tasks['H264_filed DX12'] = {
            stage('H264_filed') {
                def h264FiledVideoSourceDir = "${sourceDir}\\H264_filed"
                bat"""
                    python .\\run_regression.py -s ${ffmpegExe} -a d3d12va -i ${h264FiledVideoSourceDir}\\DX12-H264fieldConformanceTestList.txt -p ${sourceDir} -f yuv420p -o ${h264FiledVideoSourceDir}\\DX12_H264_filed_out -r 2 -n 2
                    cat ResultFail.txt > All_ResultFail.txt
                    cat All_ResultFail.txt
                    rm -rf ${h264FiledVideoSourceDir}\\DX12_H264_filed_out
                """
            }
        }

        dxva_tasks['h265 DX12'] = {
            stage('h265') {
                def h265VideoSourceDir  = "${sourceDir}\\H265"
                bat"""
                    python .\\run_regression.py -s ${ffmpegExe} -a d3d12va -i ${h265VideoSourceDir}\\DX12-HevcConfromanceTestList8bit.txt -p ${sourceDir} -f yuv420p -o ${h265VideoSourceDir}\\DX12_H265_out -r 2 -n 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    rm -rf ${h265VideoSourceDir}\\DX12_H265_out
                    python .\\run_regression.py -s ${ffmpegExe} -a d3d12va -i ${h265VideoSourceDir}\\DX12-HevcConfromanceTestList10bit.txt -p ${sourceDir} -f p010le -o ${h265VideoSourceDir}\\DX12_H265_out -r 2 -n 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    cat All_ResultFail.txt
                    rm -rf ${h265VideoSourceDir}\\DX12_H265_out
                """
            }
        }

        dxva_tasks['vp9 DX12'] = {
            stage('vp9') {
                def vp9VideoSourceDir = "${sourceDir}\\VP9"
                def vp910bitVideoSourceDir = "${sourceDir}\\VP9_10bit"
                bat"""
                    python .\\run_regression.py -s ${ffmpegExe} -a d3d12va -i ${vp9VideoSourceDir}\\DX12-VP9ConformanceTestList8bit.txt -p ${sourceDir} -f nv12 -o ${vp9VideoSourceDir}\\DX12_VP9_out -r 2 -n 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    rm -rf ${vp9VideoSourceDir}\\DX12_VP9_out
                    python .\\run_regression.py -s ${ffmpegExe} -a d3d12va -i ${vp910bitVideoSourceDir}\\DX12-VP9ConformanceTestList10bit.txt -p ${sourceDir} -f yuv420p10le -o ${vp910bitVideoSourceDir}\\DX12_VP9_10bit_out -r 2 -n 2 || exit 1
                    cat ResultFail.txt >> All_ResultFail.txt
                    cat All_ResultFail.txt
                    rm -rf ${vp910bitVideoSourceDir}\\DX12_VP9_10bit_out
                """
            }
        }

        dxva_tasks['AV1 DX12'] = {
            stage('AV1') {
                def av18bitVideoSourceDir = "${sourceDir}\\AV1_8bit"
                def av110bitVideoSourceDir = "${sourceDir}\\AV1_10bit"
                bat"""
                    python .\\run_regression.py -s ${ffmpegExe} -a d3d12va -i ${av18bitVideoSourceDir}\\DX12-AV1ConformanceTestList8bit.txt -p ${sourceDir} -f yuv420p -o ${av18bitVideoSourceDir}\\DX12_AV1_8bit_out -r 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    rm -rf ${av18bitVideoSourceDir}\\DX12_AV1_8bit_out
                    python .\\run_regression.py -s ${ffmpegExe} -a d3d12va -i ${av110bitVideoSourceDir}\\DX12-AV1ConformanceTestList10bit.txt -p ${sourceDir} -f yuv420p10le -o ${av110bitVideoSourceDir}\\DX12_AV1_10bit_out -r 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    cat All_ResultFail.txt
                    rm -rf ${av110bitVideoSourceDir}\\DX12_AV1_10bit_out
                """
            }
        }

        dxva_tasks['VC1 DX12'] = {
            stage('VC1') {
                def vc1VideoSourceDir = "${sourceDir}\\VC1"
                bat"""
                    python .\\run_regression.py -s ${ffmpegExe} -a d3d12va -i ${vc1VideoSourceDir}\\DX12-VC1ConformanceTestList.txt -p ${sourceDir} -f yuv420p -o ${vc1VideoSourceDir}\\DX12_VC1_out -r 2
                    cat ResultFail.txt >> All_ResultFail.txt
                    cat All_ResultFail.txt
                    rm -rf ${vc1VideoSourceDir}\\DX12_VC1_out
                """
            }
        }

        parallel dxva_tasks

        stage('check result') {
            def out = bat(script:'cat All_ResultFail.txt', returnStdout: true).trim()
            println '--------------'
            println out
            println out.length()
            if ( out.contains('H264') || out.contains('H265') || out.contains('vp9') || out.contains('av1') || out.contains('vc1')) {
                println 'some cases failed-----'
                print(out)
                error('error,please check ')
            }else {
                println 'Test passed'
            }
        }
    }
}

def checkTestResults(filePath) {
    def fileContent = readFile(filePath)
    def jsonContent = new JsonSlurper().parseText(fileContent)
    def caseList = jsonContent.case_list as List

    if (caseList.isEmpty()) {
        jsonContent = null
        echo 'All tests passed.'
    } else {
        jsonContent = null
        echo "Some tests failed:  ${caseList}"
        error 'Tests failed.'
    }
}

@NonCPS
def extractResFiles(String content, String fileType = 'wpix') {
    def resFilePattern = /([-\w\.]+\.${fileType})/
    def matcher = content =~ resFilePattern
    def results = []

    try {
        while (matcher.find()) {
            results << matcher.group(1)
        }
    } finally {
        matcher = null
    }

    return results
}

def checkRes(String resPath, String clistPath, String fileType = 'wpix', boolean forceDownload = false) {
    if (!fileExists(resPath)) {
        bat "md ${resPath}"
    }

    def resContent = readFile(clistPath)
    def resFiles = extractResFiles(resContent, fileType)

    if (!resFiles) {
        echo "No ${fileType} files found in ${clistPath}"
        return
    }

    def resFilesInFolder = bat(
        script: "dir /b /a:-d ${resPath}\\*.${fileType} || true",
        returnStdout: true
    ).trim()

    def sourcePath
    if (fileType == 'wpix') {
        sourcePath = '\\\\sh-samba.mthreads.com\\madmax\\ways\\wpix'
    } else {
        sourcePath = [
            '\\\\sh-samba.mthreads.com\\madmax\\ways\\renderdoc\\rdcs',
            '\\\\sh-samba.mthreads.com\\madmax\\ways\\renderdoc\\oglrdcs'
        ]
    }

    def missingFiles = resFiles.findAll { !resFilesInFolder.contains(it) }

    if (forceDownload || !missingFiles.isEmpty()) {
        dir(resPath) {
            missingFiles.each { fileName ->
                echo "Copying missing ${fileType} file: ${fileName}"
                if (fileType == 'wpix') {
                    bat "copy \"${sourcePath}\\${fileName}\" . /y"
                } else {
                    def copied = false
                    for (def i = 0; i < sourcePath.size(); i++) {
                        def srcFile = "${sourcePath[i]}\\${fileName}"
                        if (fileExists(srcFile)) {
                            bat "copy \"${srcFile}\" . /y"
                            copied = true
                            break
                        }
                    }
                    if (!copied) {
                        echo "WARNING: ${fileName} not found in any renderdoc source path"
                    }
                }
            }
        }
    } else {
        echo "All ${fileType} files are present"
    }
}

def pix_apitrace_test(String testcaseFilename = 'pix_cases_list.txt', String testCommitId = env.d3dtest) {
    def toolDir = 'D:\\win_test'
    def pixDir = "${toolDir}\\d3dtests\\gametrace\\pix"
    def pixCaseListFile = "d3dtests\\gametrace\\pix\\${testcaseFilename}"
    def testFailLog = 'test_case_fail.txt'

    dir(toolDir) {
        echo "Fetching d3dtests repository with commit ID: ${testCommitId}"
        fetchTestRepo('d3dtests', 'master', testCommitId)
        checkRes('D:\\pix', pixCaseListFile)
    }

    dir(pixDir) {
        echo "Running Pix tests in directory: ${pixDir}"

        def pixToolPath = 'C:\\Program Files\\Microsoft PIX\\2412.12\\pixtool.exe'
        def caseFilePath = ".\\${testcaseFilename}"
        def pixTestCommand = "python run_pix_test.py \"${pixToolPath}\" " +
                            "--casefile \"${caseFilePath}\" --capturedir D:\\pix " +
                            '--resultdir .\\results --goldendir .\\golden --threadnum 1'

        try {
            bat pixTestCommand
            checkTestResults(testFailLog)
        } catch (e) {
            echo "Error occurred during Pix tests: ${e.message}"
            error 'Pix test failed.'
        }
    }
}

def compiler_test(String testcaseFilename) {
    def testDir = 'D:\\win_test'

    try {
        dir(testDir) {
            fetchTestRepo('mt_compiler_test', 'master')
        }
        dir("${testDir}\\mt_compiler_test\\m3d") {
            bat'vs_m3d_build.bat'
            bat"python run_test.py --d3dtest .\\x64\\Debug\\d3dtest.exe --caselist-file ${testcaseFilename} --report report.html --parallel 8"
        }
    }
    catch (e) {
        print(e)
        publishHTML([allowMissing: true,
            alwaysLinkToLastBuild: true,
            keepAll: true,
            reportDir: "${testDir}/mt_compiler_test/m3d/tmp1",
            reportFiles: 'index.html',
            reportName: 'mt_compiler_test'
        ])
        error 'test failed!'
    }
}

def mock_test() {
    fetchTestRepo('mock', 'master')
    dir('mock') {
        bat '.\\scripts\\ci\\run_ci.bat'
    }
}

def installAndUninstallDriver_test(int testTimes = 3, String driverName = env.inf_file) {
    dir('driver') {
        def infFiles = findFiles(glob: '*.inf')
        if (infFiles.size() == 0) {
            error 'No .inf file found in driver directory!'
        }
        def inf_file_name = infFiles[0].name

        for (int runNumber = 1; runNumber <= testTimes; runNumber++) {
            def uninstallResult = bat(script: "C:\\Windows\\System32\\pnputil.exe /delete-driver ${inf_file_name} /uninstall", returnStatus: true)
            if (uninstallResult != 0) {
                echo "Driver uninstall returned code ${uninstallResult}, continuing..."
            }
            sleep(15)

            def installResult = bat(script: "C:\\Windows\\System32\\pnputil.exe /add-driver ${inf_file_name} /install", returnStatus: true)
            if (installResult != 0) {
                echo "Driver install returned code ${installResult}, checking if actually installed..."

                try {
                    def checkResult = bat(script: 'C:\\Windows\\System32\\pnputil.exe /enum-drivers | findstr "Moore Threads"', returnStatus: true)
                    echo "Driver check result code: ${checkResult}"

                    if (checkResult == 0) {
                        echo 'Driver appears to be installed successfully despite return code.'
                    } else {
                        error 'Driver installation failed for real.'
                    }
                } catch (e) {
                    echo "Driver check failed with exception: ${e.message}"
                    error 'Driver installation failed for real.'
                }
            }
            sleep(15)

            echo "Install and uninstall driver success ${runNumber}"
        }
    }
}

def vaapiFits_test(String ffmpeg_pkg_url = env.ffmpgeUrl) {
    fetchTestRepo('vaapi-fits', 'master')

    if (!ffmpeg_pkg_url) {
        def latest_url_FFmpeg = 'https://oss.mthreads.com/sw-build/FFmpeg/master/latest.txt'
        ffmpeg_pkg_url = sh(script: "curl --insecure ${latest_url_FFmpeg} -k", returnStdout: true).trim()
        ffmpeg_pkg_url += '_windows_ffmpeg.tar.gz'
    }
    def ffmpeg_pkg_name = ffmpeg_pkg_url.split('/')[-1]

    dir('ffmpeg') {
        bat """
            wget -q ${ffmpeg_pkg_url} -O ${ffmpeg_pkg_name} --no-check-certificate
            tar xvzf ${ffmpeg_pkg_name} || exit /b 1
        """
    }

    def ffmpeg_path = "${env.WORKSPACE}\\ffmpeg"
    def ffmpeg_version_output = bat(script: "${ffmpeg_path}\\ffmpeg.exe -version", returnStdout: true)
    echo "FFmpeg version:\n${ffmpeg_version_output}"

    dir('vaapi-fits') {
        bat "set PATH=${ffmpeg_path};%PATH% && start.bat"
    }
}

def whlkVideo_test() {
    String testDir = 'D:\\win_test'

    if (!fileExists("${testDir}\\whlktest")) {
        dir(testDir) {
            fetchTestRepo('whlktest', 'master')
        }
    }

    dir("${testDir}\\whlktest\\hlk_video_for_ci") {
        if (!fileExists('hlk_video')) {
            bat '''
                xcopy \\\\************\\share\\whlk\\hlk_video_ci\\hlk_video_win11\\hlk_video .\\hlk_video /E /I /Y
            '''
        }

        bat 'run.bat'

        def txt_list = ['result.txt']
        dir('hlk_log') {
            txt_list.each { resultFile ->
                def out = bat(script: "cat ${resultFile}", returnStdout: true).trim()
                println("Contents of ${resultFile}:\n${out}")

                if (out.toLowerCase().contains('fail') || out.toLowerCase().contains('miss')) {
                    error('whlk video test failed !')
                } else {
                    println 'whlk video test passed !'
                }
            }
        }
    }
}

def setOpenGl(String version = '') {
    if (!version) {
        try {
            dir("${env.WORKSPACE}\\driver\\symbols") {
                bat 'oglpanel.exe --defaultAll'
            }
            echo 'OpenGL settings reset to default'
            return
        } catch (e) {
            echo "Failed to reset OpenGL settings: ${e.message}"
            throw e
        }
    }

    Map openGlConfigs = [
        '40': 'EnableOGL40.cfg',
        '41': 'EnableOGL41.cfg',
        '42': 'EnableOGL42.cfg',
        '43': 'EnableOGL43.cfg',
        '44': 'EnableOGL44.cfg',
        '45': 'EnableOGL45.cfg',
        '46': 'EnableOGL46.cfg'
    ]

    def openGlConfig = openGlConfigs[version]
    if (openGlConfig == null) {
        echo "Unsupported OpenGL version: ${version}"
        return
    }

    try {
        dir("${env.WORKSPACE}\\driver\\symbols") {
            bat "oglpanel.exe --import D:\\tools\\${openGlConfig}"
        }
        echo "OpenGL version ${version} set successfully."
    } catch (e) {
        echo "Failed to set OpenGL version ${version}: ${e.message}"
        throw e
    }
}

def ogl_mtglcts_test(String caselist = 'passlist-mtglcts.txt', String oglTestCommitId = env.ogltest) {
    def baseDir = caselist.contains('vps') ? 'C:\\win_test' : 'D:\\win_test'
    def case_file = "${baseDir}\\ogltests\\passlists\\${caselist}"

    prepare_ogltest(['mtgl_cts'], "${baseDir}\\ogltests\\passlists")

    dir(baseDir) {
        fetchTestRepo('ogltests', 'master', oglTestCommitId)
    }

    print 'mtgl_cts repository fetched.'

    try {
        dir('mtgl_cts/script/') {
            bat '''
                .\\install.bat compile
            '''
        }
    } catch (e) {
        error "Error during compile: ${e.message}"
    }

    dir('mtgl_cts/script/') {
        bat """
            .\\install.bat test ${case_file}
        """
    }

    dir('mtgl_cts') {
        def testFiles = [
            'mtgl_cts_compatibility33_result.xml',
            'mtgl_cts_core33_result.xml',
            'mtgl_cts_core40_result.xml',
            'mtgl_cts_core41_result.xml',
            'mtgl_cts_core42_result.xml',
            'mtgl_cts_core43_result.xml'
        ]

        testFiles.each { testFile ->
            if (fileExists(testFile)) {
                withChecks("Integration Tests - ${testFile}") {
                    junit testFile
                }
            }
        }
    }

    if (currentBuild.result == 'UNSTABLE') {
        error('currentBuild.result : Unstable')
    }

    // Check test results
    def resultFile = 'mtgl_cts/mtglcts_result.txt'
    if (!fileExists(resultFile)) {
        error('mtgl_cts test fail: Result file not found!')
    }

    def fileContent = readFile(resultFile)
    echo "File Content:\n${fileContent}"

    // Extract failed count using pattern matching
    def failedPattern = /Failed:\s*(\d+)/
    def matcher = fileContent =~ failedPattern

    if (matcher) {
        def failCount = matcher[0][1].toInteger()
        matcher = null  // Clear matcher

        if (failCount == 0) {
            echo 'mtgl_cts test success!'
        } else {
            error("mtgl_cts test fail! Failed count: ${failCount}")
        }
    } else {
        error('mtgl_cts test fail: Could not find Failed count in results')
    }
}

@NonCPS
def parsePiglitResults(String output) {
    def regexPass = /\[(\d+)\/(\d+)\].*pass: (\d+)/
    def regexFail = /\[(\d+)\/(\d+)\].*fail: (\d+)/

    List<Serializable> matchesPass = []
    List<Serializable> matchesFail = []

    try {
        // Process pass matches
        output.eachMatch(regexPass) { match ->
            def passCount = (match[3] ?: '0') as Integer
            def totalCount = (match[2] ?: '0') as Integer
            matchesPass.add([
                passCount: passCount,
                totalCount: totalCount
            ])
        }

        // Process fail matches
        output.eachMatch(regexFail) { match ->
            def failCount = (match[3] ?: '0') as Integer
            def totalCount = (match[2] ?: '0') as Integer
            matchesFail.add([
                failCount: failCount,
                totalCount: totalCount
            ])
        }
    } catch (e) {
        println "Error parsing results: ${e.message}"
    }

    return [pass: matchesPass, fail: matchesFail]
}

def ogl_piglit_test(String caselist, String testCommitId = '', String oglTestCommitId = env.ogltest) {
    def baseDir = caselist.contains('vps') ? 'C:\\win_test' : 'D:\\win_test'
    def case_file = "${baseDir}\\ogltests\\passlists\\${caselist}"

    dir(baseDir) {
        fetchTestRepo('ogltests', 'master', oglTestCommitId)
    }

    //if the passlist needs to specify a version, specify the opengl version
    stage('Piglit Test setup') {
        def oglVersion = caselist.replaceAll('[^0-9]', '')
        setOpenGl(oglVersion)
    }

    stage('Run test') {
        try {
            dir('D:\\ogl') {
                if (testCommitId != '') {
                    fetchTestRepo('piglit', 'mt/main', testCommitId)
                    dir('piglit') {
                        if (fileExists('.gitmodules')) {
                            new git().updateSubmodule('piglit')
                        } else {
                            echo 'No submodules found in piglit'
                        }
                    }
                    timeout(30) {
                        dir('D:\\ogl\\piglit') {
                            def DC_File = 'D:\\ogl\\vcpkg\\scripts\\buildsystems\\vcpkg.cmake'
                            def DW_Dir = 'D:\\ogl\\waffle-1.8.0\\include\\waffle-1'
                            def DW_Lib = 'D:\\ogl\\waffle-1.8.0\\lib\\waffle-1.lib'
                            bat """cmake . -G "Visual Studio 16 2019" -A x64 -DCMAKE_TOOLCHAIN_FILE=${DC_File} -DPIGLIT_USE_WAFFLE=1  -DWaffle_INCLUDE_DIRS=${DW_Dir} -DWaffle_LDFLAGS=${DW_Lib}"""
                            echo 'cmake done'
                            bat 'cmake --build . --config Debug -j 12'
                            echo 'build done'
                        }
                    }
                }
            }
            dir('D:\\ogl\\piglit-rs\\html') {
                deleteDir()
            }
            dir('D:\\ogl\\piglit-rs\\data') {
                deleteDir()
            }

            dir('D:\\ogl\\piglit') {
                def output = bat(
                    script: "set PATH=D:\\ogl\\waffle-1.8.0\\bin;%PATH% && python ./piglit run all --test-list ${case_file} --overwrite ../piglit-rs/data --verbose",
                    returnStdout: true
                )
                println output

                def results = parsePiglitResults(output)

                if (!results.pass.isEmpty()) {
                    def lastMatch = results.pass[-1]
                    echo "Pass count:${lastMatch.passCount}"
                    echo "Total count:${lastMatch.totalCount}"
                    if (lastMatch.passCount != lastMatch.totalCount) {
                        if (env.gitlabSourceRepoName == 'm3d') {
                            echo 'Test failed! But always pass. To avoid introducing regressions please check the test results'
                        } else {
                            error 'Test failed!'
                        }
                    } else {
                        echo 'Test pass! But always pass~ 0_0'
                    }
                }

                if (!results.fail.isEmpty()) {
                    def lastMatch = results.fail[-1]
                    echo "Fail count:${lastMatch.failCount}"
                    echo "Total count:${lastMatch.totalCount}"
                    if (lastMatch.failCount != 0) {
                        error 'Test failed! Please check the test results'
                    }
                }

                bat 'python .\\piglit summary html ..\\piglit-rs\\html ..\\piglit-rs\\data'
                publishHTML([
                    allowMissing: true,
                    alwaysLinkToLastBuild: true,
                    keepAll: true,
                    reportDir: 'D:\\ogl\\piglit-rs\\html',
                    reportFiles: 'index.html',
                    reportName: 'Piglit Results'
                ])
            }
        } catch (e) {
            print(e)
            error "Error during test execution: ${e.message}"
        } finally {
            setOpenGl()
        }
    }
}

def ogl_cts_test(String caselist, String executable = 'glcts.exe', String oglTestCommitId = env.ogltest) {
    def baseDir = caselist.contains('vps') ? 'C:\\win_test' : 'D:\\win_test'
    def case_file = "${baseDir}\\ogltests\\passlists\\${caselist}"
    if (env.driverType.contains('newapi')) {
        case_file = "${baseDir}\\ogltests\\passlists\\newapi\\${caselist}"
    }

    dir(baseDir) {
        fetchTestRepo('ogltests', 'master', oglTestCommitId)
    }

    String oglVersion = ''
    try {
        def versionPattern = /-(\d+)\.(\d+)\.txt$/
        def matcher = caselist =~ versionPattern
        if (matcher.find()) {
            oglVersion = matcher.group(1) + matcher.group(2)
            matcher = null
        }
    } catch (e) {
        println "Failed to extract OpenGL version: ${e.message}"
    }

    // Set OpenGL version if extracted
    try {
        /* groovylint-disable-next-line UnnecessarySetter */
        setOpenGl(oglVersion)
    } catch (e) {
        println "Failed to set OpenGL version: ${e.message}"
    }

    try {
        stage('download CTS tool') {
            dir("${env.WORKSPACE}\\cts") {
                def ctsDir = caselist.contains('passlist-gles') ? 'gles' : 'gl'
                def ctsPkg = caselist.contains('passlist-gles') ? 'opengl-es-cts-3.2.7.0-windows-release.7z' : 'opengl-cts-*******-windows-release.7z'
                bat """
                    wget -q --no-check-certificate https://oss.mthreads.com/product-release/cts/${ctsDir}/${ctsPkg}
                    7z x ${ctsPkg} -aoa
                """
            }
        }
        stage('Run CTS') {
            // Run CTS test
            timeout(20) {
                def ctsExtractDir = caselist.contains('passlist-gles')
                    ? "${env.WORKSPACE}\\cts\\opengl-es-cts-3.2.7.0-windows-release"
                    : "${env.WORKSPACE}\\cts\\opengl-cts-*******-windows-release"
                dir(ctsExtractDir) {
                    def test_cmd = ".\\${executable} --deqp-caselist-file=${case_file}"
                    if (caselist.contains('passlist-gles')) {
                        ['libEGL.dll', 'libGLESv1_CM.dll', 'libGLESv2.dll'].each { dll ->
                            bat "copy /Y ${env.WORKSPACE}\\driver\\${dll} ."
                        }
                        test_cmd += ' --deqp-gl-context-type=egl'
                    }
                    def test_result = bat(script: "${test_cmd}", returnStdout: true).trim()
                    echo "${test_result}"

                    // Check result log
                    if (checkPassRate(test_result)) {
                        println 'Test passed'
                    } else {
                        error 'test failed!'
                    }
                }
            }
        }
    } catch (e) {
        error "Error during test execution: ${e.message}"
    } finally {
        setOpenGl()
    }
}

def mt_trace_test(String testcaseFilename = 'hg_smoke_ci_list.txt', String architecture = 'x64', String testCommitId = env.d3dtest) {
    String testDir = 'C:\\win_test'

    dir(testDir) {
        if (env.gitlabSourceRepoName == 'diagsys-ci') {
            new git().fetchCode(env.gitlabSourceRepoName, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true, updateBuildDescription: true])
        } else {
            fetchTestRepo('diagsys-ci', 'master')
        }
    }

    downloadAndExtractD3dTest(testCommitId, architecture)

    stage('capture mt trace') {
        dir(testDir) {
            // Copy files from diagsys-ci repository structure
            bat """
                copy /Y diagsys-ci\\wddm\\script\\capture_mt_trace.py .
                copy /Y diagsys-ci\\wddm\\case_list\\${testcaseFilename} .
                copy /Y diagsys-ci\\wddm\\bin\\mt_trace_capturer.exe .
            """

            powershell """
                python capture_mt_trace.py --test C:\\Test\\d3dtest.exe --file ${testcaseFilename} --out mt_traces
            """

            trace_file_path = constants.genOssPath(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestIid)
            oss.setUp()

            bat """
                tar -cvzf mt_traces.tar.gz  .\\mt_traces
                mc cp mt_traces.tar.gz oss/${trace_file_path}/
            """
        }
    }

    node(env.replay_Nodes) {
        stage('replay trace files') {
            try {
                currentBuild.description += "Linux Node: ${env.NODE_NAME} <br>"
                deleteDir()

                // Clone diagsys-ci to get the script locally
                fetchTestRepo('diagsys-ci', 'master')

                sh """
                    cp diagsys-ci/common/run_mt_trace.sh .
                    chmod 755 run_mt_trace.sh
                    ./run_mt_trace.sh -u 0 -d oss/${trace_file_path}/mt_traces.tar.gz -m hg -a ${env.hgvpstag}
                """
            }catch (e) {
                print(e)
                error 'mttrace test fail!'
            }
        }
    }
}

def prepare_ogltest(List<String> reposToFetch = ['ogltests', 'ogl-samples', 'MT_LearnOpenGL'], String passlistDir = 'driver') {
    def repos = [
        ['name': 'ogl-samples', 'file': 'passlist-gTruc.txt'],
        ['name': 'MT_LearnOpenGL', 'file': 'passlist-learnogl.txt'],
        ['name': 'mtgl_cts', 'file': 'passlist-mtglcts.txt']
    ]

    def repoNames = repos*.name
    def allReposExist = repoNames.every { fileExists(it) }

    if (allReposExist) {
        println 'Ogltests already included, skipping preparation.'
        return
    }

    repos.each { repo ->
        if (reposToFetch.contains(repo.name) && !fileExists(repo.name)) {
            def commitId = getcommit(passlistDir, repo.file)
            new git().fetchCode(repo.name, 'master', commitId, [preBuildMerge: false, disableSubmodules: true, updateBuildDescription: true])
        }
    }
}

def getResults(String cmdLine) {
    return bat(script: "chcp 65001 && set LANG=en_US.UTF-8 && ${cmdLine}", returnStdout: true).trim()
}

def ogl_pg3e_test(String caselist = 'passlist-pg3e.txt', String oglTestCommitId = env.ogltest) {
    def baseDir = caselist.contains('vps') ? 'C:\\win_test' : 'D:\\win_test'
    def case_file = "${baseDir}\\ogltests\\passlists\\${caselist}"

    dir(baseDir) {
        fetchTestRepo('ogltests', 'master', oglTestCommitId)
    }

    String pg3eTestDir = "${baseDir}\\ogltests\\Pg3eTest"

    dir("${pg3eTestDir}") {
        dir('build') {
            bat '''
                cmake ..
                cd ..
                cmake --build ./build --config Debug
            '''
        }
        bat 'md bin && cp -rf build/Debug/*.exe bin'
        bat """
            copy ${env.WORKSPACE}\\driver\\mticdg64.dll   ${baseDir}\\ogltests\\Pg3eTest\\bin\\ /y
        """
        def oglResult = getResults("python get_pg3etest_results.py ${case_file}")
        println oglResult
        if (checkPassRate(oglResult)) {
            println 'Test passed'
        } else {
            error 'test failed!'
        }
    }
}

def ogl_gTruc_test(String caselist = 'passlist-gTruc.txt', String oglTestCommitId = env.ogltest) {
    String gTrucTestDir = 'ogl-samples'

    def baseDir = caselist.contains('vps') ? 'C:\\win_test' : 'D:\\win_test'
    def case_file = "${baseDir}\\ogltests\\passlists\\${caselist}"

    prepare_ogltest(['ogl-samples'], "${baseDir}\\ogltests\\passlists")

    dir(baseDir) {
        fetchTestRepo('ogltests', 'master', oglTestCommitId)
    }

    bat '''
        copy driver\\mticdg64.dll   ogl-samples\\ /y
    '''

    dir("${gTrucTestDir}") {
        def oglResult = getResults("python get_g-truc_results.py ${case_file} BRC")
        println oglResult

        if (checkPassRate(oglResult)) {
            println 'Test passed'
        } else {
            error 'test failed!'
        }
    }
}

def checkPassRate(String result) {
    def normalizedResult = result.replaceAll('\\s+', ' ').trim()

    return normalizedResult.contains('pass rate: 100.000000%') ||
           normalizedResult.contains('Failed: 0/') ||
           normalizedResult.contains('fail: 0') ||
           normalizedResult.contains('Fail: 0')
}

def ogl_learnogl_test(String caselist = 'passlist-learnogl.txt', String oglTestCommitId = env.ogltest) {
    def baseDir = caselist.contains('vps') ? 'C:\\win_test' : 'D:\\win_test'
    def case_file = "${baseDir}\\ogltests\\passlists\\${caselist}"

    prepare_ogltest(['MT_LearnOpenGL'], "${baseDir}\\ogltests\\passlists")

    dir(baseDir) {
        fetchTestRepo('ogltests', 'master', oglTestCommitId)
    }

    bat '''
        copy driver\\mticdg64.dll   MT_LearnOpenGL\\ /y
    '''

    dir('MT_LearnOpenGL') {
        bat 'python copy_drv.py'
        def oglResult = getResults("python get_learnogl_results.py ${case_file}")
        println oglResult
        if (checkPassRate(oglResult)) {
            println 'Test passed'
        } else {
            error 'test failed!'
        }
    }
}

@NonCPS
def extractTraceFiles(String content) {
    def traceFilePattern = /^(?!(?:#|\/\/))([-\w\.]+\.trace)/
    def results = []
    try {
        content.readLines().each { line ->
            def lineMatcher = line =~ traceFilePattern
            if (lineMatcher.find()) {
                results << lineMatcher.group(1)
            }
            lineMatcher = null  // Clean up matcher
        }
    } catch (e) {
        echo "Error parsing trace files: ${e.message}"
    }

    // Log results
    if (results.isEmpty()) {
        echo 'WARNING: No trace files found in the content'
        echo 'Content was:'
        echo content
    } else {
        echo 'Found trace files:'
        results.each { echo "  - ${it}" }
    }

    return results
}

def ogl_apitrace_test(String caselist, String oglTestCommitId = env.ogltest) {
    try {
        def baseDir = caselist.contains('vps') ? 'C:\\win_test' : 'D:\\win_test'
        def case_file = "${baseDir}\\ogltests\\passlists\\${caselist}"

        dir(baseDir) {
            fetchTestRepo('ogltests', 'master', oglTestCommitId)
        }

        stage('fetch ogl_apitrace test') {
            bat """
                md apitraceTest
                wget -q --no-check-certificate https://swci-oss.mthreads.com/dependency/swci/apitrace/apitrace-latest-win64-gl.7z -O apitraceTest\\apitrace-latest-win64-gl.7z
                xcopy ${baseDir}\\ogltests\\appTraceTest\\   apitraceTest\\ /s /e /y
                copy ${env.WORKSPACE}\\driver\\mticdg64.dll   apitraceTest\\ /y
                cd apitraceTest && 7z x apitrace-latest-win64-gl.7z || exit 1
            """

            if (!fileExists('D:\\ogltrace')) {
                bat 'md D:\\ogltrace'
            }

            def passListContent = readFile("${case_file}")
            def traceFiles = extractTraceFiles(passListContent)

            def traceFilesInFolder = bat(
                script: 'dir /b /a:-d D:\\ogltrace\\*.trace || true',
                returnStdout: true
            ).trim().split('\r\n')

            def missingFiles = traceFiles.findAll { !traceFilesInFolder.contains(it) }

            if (missingFiles.isEmpty()) {
                println('All TraceFiles Good')
            } else {
                missingFiles.each { traceFile ->
                    println(traceFile)
                    dir('D:\\ogltrace') {
                        bat "mc cp -r oss/release-ci/ogl/tools/apitrace/traces/${traceFile} ."
                    }
                }
            }
        }

        stage('run ogl_apitrace') {
            dir("${env.WORKSPACE}\\apitraceTest") {
                def result = bat(
                    script: """
                        chcp 65001
                        set LANG=en_US.UTF-8
                        python get_apitraceTests_results.py ${case_file} D:\\ogltrace\\
                    """,
                    returnStdout: true
                ).trim()

                echo "Test Result:\n${result}"

                if (!checkPassRate(result)) {
                    error 'ogl_apitrace test fail!'
                }
            }
        }
    } catch (e) {
        print(e)
        error 'ogl_apitrace test fail!'
    }
}

@NonCPS
def extractCommitId(String content) {
    def matcher = content =~ /commit\s?id:\s?(\w{9})/
    try {
        if (matcher.find()) {
            return matcher.group(1)
        }
    } finally {
        matcher = null
    }
    return ''
}

def getcommit(String clistpath, String passlist) {
    try {
        def filePath = "${clistpath}\\${passlist}"
        if (!fileExists(filePath)) {
            println "File not found: ${filePath}"
            return ''
        }

        def firstLine = readFile(filePath).readLines().first()
        def commitId = extractCommitId(firstLine)

        if (commitId) {
            println "${passlist} commitid: ${commitId}"
            return commitId
        }

        println "No valid commit ID found in: ${passlist}"
        return ''
    } catch (e) {
        println "Error in getcommit: ${e.message}"
        return ''
    }
}

def getCaseFileContent(filePath) {
    echo "Reading case file content from: ${filePath}"
    try {
        def content = bat(script: "type \"${filePath}\"", returnStdout: true).trim()

        // Clean the content by removing command echo lines
        def lines = content.split('\n')
        def cleanLines = lines.findAll { line ->
            !line.contains('>type ') && !line.trim().isEmpty()
        }
        def cleanContent = cleanLines.join('\n')

        echo "Successfully read ${cleanContent.split('\n').size()} lines from file"
        echo 'File content preview (first 3 lines):'
        cleanContent.split('\n').take(3).eachWithIndex { line, index ->
            echo "  Line ${index + 1}: ${line}"
        }
        return cleanContent
    } catch (e) {
        echo "Error reading file ${filePath}: ${e.message}"
        throw e
    }
}

/* groovylint-disable-next-line NoFloat */
def ogl_pdump_test(String caselist= 'passlist_pdump.txt', float similarityThreshold = 0.99 , String oglTestCommitId = env.ogltest) {
    lock("pdump_test_${env.BUILD_NUMBER}") {
        def ossBranchPath = constants.genOssPath(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestIid)
        def ossAlias = constants.genOssAlias(ossBranchPath)
        def pdump_file_path = "${ossAlias}/${ossBranchPath}/oglpdump/"
        bat 'md pdump'

        def destination = "${env.WORKSPACE}\\pdump"
        def filesToCopy = ['pdump.exe', 'mticdg64.dll']

        dir('driver') {
            bat filesToCopy.collect { file ->
                "copy /Y ${file} ${destination}"
            }.join(' && ')
        }

        def baseDir = 'C:\\win_test'
        def case_file = "${baseDir}\\ogltests\\passlists\\${caselist}"

        dir(baseDir) {
            fetchTestRepo('ogltests', 'master', oglTestCommitId)
        }

        case_file_content = getCaseFileContent(case_file)

        bat """
            copy /Y  ${baseDir}\\ogltests\\pdumpTest\\get_pdump_log_files.py  ${env.WORKSPACE}\\pdump\\
        """
        timeout(5) {
            dir('pdump') {
                bat """
                    wget -q https://oss.mthreads.com/release-ci/ogl/tools/pdump/pdump_ci_winogl.tar.gz --no-check-certificate
                    tar xvzf pdump_ci_winogl.tar.gz
                    start "python" get_pdump_log_files.py ${case_file} 30
                    sleep 120
                """
            }
        }

        oss.setUp()

        bat """
            mc cp -r pdump\\pdump_log_files\\ ${pdump_file_path}
        """

        node(env.replay_Nodes) {
            currentBuild.description += "Linux Node: ${env.NODE_NAME} <br>"
            deleteDir()
            timeout(15) {
                stage('replay pdump files') {
                    fetchTestRepo('ogltests', 'master', oglTestCommitId)

                    oss.install()

                    retry(3) {
                        sh "mc cp -r ${pdump_file_path} ./ && ls -lh"

                        def fileExists = sh(script: "find . -name 'out2.txt'", returnStatus: true) == 0

                        if (!fileExists) {
                            sleep(time: 10, unit: 'SECONDS')
                            error 'out2.txt not found, retrying...'
                        } else {
                            echo 'out2.txt found!'
                        }
                    }

                    for (test in case_file_content.split('\n')) {
                        def case_name = test.trim().split(',')[1]
                        def compare_file = test.trim().split(',')[3]
                        dir(case_name) {
                            sh'''
                                mc cp -r oss/release-rc/vps/9.3/soc_model/release_mode/cmodel/quyuan2/sim/libExtractImage64.so ./
                                mc cp -r oss/release-rc/vps/9.3/soc_model/release_mode/cmodel/quyuan2/sim/libPdumpPlayer64.so ./
                                mc cp -r oss/release-rc/vps/16.0/soc_model/release_mode/cmodel/quyuan2/libsim_transif1.so ./
                                mc cp -r oss/release-rc/vps/16.0/soc_model/release_mode/cmodel/quyuan2/libsystemc-2.3.3.so ./
                                mc cp -r oss/release-rc/vps/16.0/soc_model/release_mode/cmodel/quyuan2/sim/ ./
                            '''
                            def sign_driver_json = readJSON file: 'config.json' , returnPojo: true
                            sign_driver_json['PdumpTools']['pdump2']['verify']['type'] = 'Save_file'
                            writeJSON file: 'config.json', json: sign_driver_json
                            sh '''
                                chmod 755 sim
                                export LD_LIBRARY_PATH=./
                                ./sim --fb-bitmap-output || true
                            '''
                            def sim_out_files = sh(script: 'ls sim_outfb*', returnStdout: true).trim()
                            print(sim_out_files)
                            for (sim_out_file in sim_out_files.split('\n')) {
                                def sim_out_file_md5 = sh(script: "md5sum ${sim_out_file}", returnStdout: true).trim()
                                print sim_out_file_md5
                            }
                            sh """
                                magick compare -metric SSIM ${compare_file} ${env.WORKSPACE}/ogltests/pdumpTest/golden/${case_name}.bmp diff.bmp > logs.txt 2>&1 || true

                                rm -f sim_outfb*
                            """
                            def result_log = sh(script: 'cat logs.txt', returnStdout: true).trim()
                            echo "${result_log}"
                            if (result_log.toFloat() >= similarityThreshold) {
                                print 'ogl pdump test pass'
                            } else {
                                error 'ogl pdump test fail'
                            }
                        }
                    }
                }
            }
        }
    }
}

def d3d_pdump_test(String case_name, String start_frame, String exec_param) {
    bat """
        start  "pdump" /D ${WORKSPACE}\\driver\\  /B ${WORKSPACE}\\driver\\pdump.exe -fo${case_name} -${start_frame}
        start "d3dtest.exe" /D C:\\Test\\ /B  C:\\Test\\d3dtest.exe -t ${exec_param} -frames 3 -end true -width 100 -height 100
        sleep 20
    """
}

def pdump_test(String caselist = 'pdumplist_ph1.txt', String testCommitId = env.d3dtest, String vpsTag = env.ph1vpstag) {
    lock("pdump_test_${env.BUILD_NUMBER}") {
        def ossBranchPath = constants.genOssPath(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestIid)
        def ossAlias = constants.genOssAlias(ossBranchPath)
        def pdump_file_path = "${ossAlias}/${ossBranchPath}/pdump/"
        String testDir = 'C:\\win_test'
        def pdumpFilePath = "C:/win_test/d3dtests/function/${caselist}"

        dir(testDir) {
            fetchTestRepo('d3dtests', 'master', testCommitId)
        }

        downloadAndExtractD3dTest(testCommitId, 'x64')

        bat 'md pdump'
        case_file_content = sh(script: "cat ${pdumpFilePath}", returnStdout: true).trim()
        for (case_file in case_file_content.split('\n')) {
            def case_parts = case_file.trim().split(',')
            if (case_parts.size() < 4) {
                continue
            }
            def case_name = case_parts[0].trim()
            def start_frame = case_parts[1].trim()
            def exec_param = case_parts[2].trim()
            def golden_files = case_parts[3].trim().split(' ')

            timeout(10) {
                d3d_pdump_test(case_name, start_frame, exec_param)
            }
            bat "COPY /Y ${WORKSPACE}\\driver\\${case_name}2.* pdump\\"
            def copyCommands = golden_files.collect { golden_file ->
                "COPY /Y ${testDir}\\d3dtests\\function\\golden\\PH1\\${golden_file} pdump\\"
            }.join(' & ')

            bat copyCommands
        }

        oss.setUp()

        bat """
            mc cp -r pdump\\ ${pdump_file_path}
        """

        node(env.replay_Nodes) {
            try {
                currentBuild.description += "Linux Node: ${env.NODE_NAME} <br>"
                deleteDir()

                stage('get pdump files') {
                    sh """
                        mc alias set oss https://oss.mthreads.com mtoss mtoss123
                        mc cp -r ${pdump_file_path}   ./
                        mc cp -r oss/release-ci/vps/amodel_release/ci_amodel_standalone_mode_v2.0.tar.gz   ./
                        tar xvzf ci_amodel_standalone_mode_v2.0.tar.gz
                    """
                }

                timeout(20) {
                    stage('replay pdump files amodel') {
                        sh """
                            mc cp -r oss/release-rc/dog/PdumpExtractImage   ./ci_amodel_standalone_mode_v2.0
                            mc cp oss/mt-amodel-release/ph1_daily/${vpsTag}/libchiplib.so ./ci_amodel_standalone_mode_v2.0/ph1/
                            mc cp oss/mt-amodel-release/ph1_daily/${vpsTag}/libsystemc.so ./ci_amodel_standalone_mode_v2.0/ph1/
                            mc cp oss/release-rc/vps/misc/dx_ci/gfx_config.yaml ./ci_amodel_standalone_mode_v2.0/
                            sed -i 's/shared_memory_enable: true/shared_memory_enable: false/' ./ci_amodel_standalone_mode_v2.0/gfx_config.yaml
                            chmod -R 755 ./ci_amodel_standalone_mode_v2.0
                        """
                        for (case_file in case_file_content.split('\n')) {
                            def case_parts = case_file.trim().split(',')
                            if (case_parts.size() < 4) {
                                continue
                            }
                            def case_name = case_parts[0].trim()
                            def golden_files = case_parts[3].trim().split(' ')

                            sh """
                                mv ${case_name}2.prm out2.prm
                                mv ${case_name}2.txt out2.txt
                            """
                            dir('ci_amodel_standalone_mode_v2.0') {
                                sh './run_case.sh .. -c ph1'
                                def pdump_log = sh(script: 'cat pdump.log', returnStdout: true).trim()
                                print("pdump log:${pdump_log}")
                                for (golden_file in golden_files) {
                                    def image_file = golden_file.substring(case_name.length() + 1)
                                    if (fileExists(image_file)) {
                                        sh "cmp ${image_file} ../${golden_file} >> logs.txt || true"
                                    }else {
                                        error "No such ${image_file} !"
                                    }
                                }

                                def result_log = sh(script: 'cat logs.txt', returnStdout: true).trim()
                                print("cmp log:${result_log}")
                                if (result_log.isEmpty() && pdump_log.contains('All Tests Passed!')) {
                                    print("${case_name} test pass")
                                }
                                else {
                                    error "${case_name} test fail"
                                }
                            }
                            sh '''
                                rm -rf out2.prm
                                rm -rf out2.txt
                            '''
                        }
                    }
                }
            }catch (e) {
                print(e)
                error 'amodel pdump test fail'
            }//catch
        }

        node(env.replay_Nodes) {
            try {
                currentBuild.description += "Linux Node: ${env.NODE_NAME} <br>"
                deleteDir()

                stage('get pdump files') {
                    dir('arch_DOG_dbg_binary') {
                        sh """
                            mc alias set oss https://oss.mthreads.com mtoss mtoss123
                            mc cp -r ${pdump_file_path}   ./
                            mc cp -r oss/mt-cmodel-release/ph1_daily/${vpsTag}/arch_DOG_dbg_binary.tar.gz   ./
                            tar xvzf arch_DOG_dbg_binary.tar.gz
                        """
                    }
                }

                timeout(20) {
                    stage('replay pdump files cmodel') {
                        sh '''
                            mc cp -r oss/release-rc/dog/PdumpExtractImage   ./arch_DOG_dbg_binary/ci_env/transif_binary
                            mc cp oss/release-rc/vps/misc/dx_ci/gfx_config.yaml ./arch_DOG_dbg_binary/ci_env/transif_binary/
                            mc cp oss/madmax/zhengqiang/app_test_config.json ./arch_DOG_dbg_binary/ci_env/transif_binary/
                            chmod -R 755 ./arch_DOG_dbg_binary
                        '''
                        for (case_file in case_file_content.split('\n')) {
                            def case_parts = case_file.trim().split(',')
                            if (case_parts.size() < 4) {
                                continue
                            }
                            def case_name = case_parts[0].trim()
                            def golden_files = case_parts[3].trim().split(' ')
                            dir('arch_DOG_dbg_binary') {
                                sh """
                                    mv ${case_name}2.prm ./ci_env/transif_binary/out2.prm
                                    mv ${case_name}2.txt ./ci_env/transif_binary/out2.txt
                                """
                            }
                            dir('arch_DOG_dbg_binary/ci_env/transif_binary') {
                                def currentDir = sh(script: 'pwd', returnStdout: true).trim()
                                sh """
                                    export LD_LIBRARY_PATH=$currentDir
                                    ./DOG --test . --config app_test_config.json
                                """
                                def pdump_log = sh(script: 'cat pdump.log', returnStdout: true).trim()
                                print("pdump log:${pdump_log}")
                                for (golden_file in golden_files) {
                                    def image_file = golden_file.substring(case_name.length() + 1)
                                    if (fileExists(image_file)) {
                                        sh "cmp ${image_file} ../../${golden_file} >> logs.txt || true"
                                    }else {
                                        error "No such ${image_file} !"
                                    }
                                }

                                def result_log = sh(script: 'cat logs.txt', returnStdout: true).trim()
                                print("cmp log:${result_log}")
                                if (result_log.isEmpty() && pdump_log.contains('All Tests Passed!')) {
                                    print("${case_name} test pass")
                                }
                                else {
                                    error "${case_name} test fail"
                                }
                                sh '''
                                    rm -rf out2.prm
                                    rm -rf out2.txt
                                '''
                            }
                        }
                    }
                }
            }catch (e) {
                print(e)
                error 'cmodel pdump test fail'
            }//catch
        }
    }
}

def compilerDumps_test(String testCommitId = env.d3dtest) {
    String testDir = 'D:\\win_test'
    String bin_path = 'C:\\renderdoc_release\\Release'
    String log_path = "${testDir}\\d3dtests\\gametrace\\renderdoc\\pipelineDump.log"

    def log_files = ['pipelineDump.log']

    dir("${testDir}\\d3dtests\\gametrace\\renderdoc") {
        deleteLogFiles(log_files)
    }

    try {
        dir(testDir) {
            fetchTestRepo('d3dtests', 'master', testCommitId)
            if (!fileExists('compilerDumpsSettings.dat')) {
                bat '''
                    copy \\\\************\\share\\CI\\win-sync\\compilerDumpsSettings.dat .\\compilerDumpsSettings.dat /Y
                '''
            }
        }

        dir('driver\\symbols') {
            bat "start mtpanel.exe -file ${testDir}\\compilerDumpsSettings.dat"
        }

        dir("${testDir}\\d3dtests\\gametrace\\renderdoc") {
            bat "set PYTHONPATH=${bin_path} && C:\\Python\\Python38\\python.exe replay_rdc.py .\\rdc_cases_list_pipelineDumps.txt \\\\sh-samba.mthreads.com\\madmax\\ways\\renderdoc\\rdcs  results .\\golden >> ${log_path}"
        }

        def test_result = bat(script: "cat ${log_path}", returnStdout: true).trim()
        echo "${test_result}"

        //check result log
        if (checkPassRate(test_result)) {
            println 'Test passed'
        } else {
            error 'test failed!'
        }
    } catch (e) {
        error("An error occurred: ${e.message}")
    } finally {
        dir('driver\\symbols') {
            bat 'start mtpanel.exe -DefaultALL'
        }
    }
}

def ogl_cts32_test(String caselist, String oglTestCommitId = env.ogltest, String executable = 'glcts32.exe') {
    def baseDir = caselist.contains('vps') ? 'C:\\win_test' : 'D:\\win_test'
    def case_file = "${baseDir}\\ogltests\\passlists\\${caselist}"
    if (env.driverType.contains('newapi')) {
        case_file = "${baseDir}\\ogltests\\passlists\\newapi\\${caselist}"
    }

    dir(baseDir) {
        fetchTestRepo('ogltests', 'master', oglTestCommitId)
    }

    String oglVersion = ''
    try {
        def versionPattern = /-(\d+)\.(\d+)\.txt$/
        def matcher = caselist =~ versionPattern
        if (matcher.find()) {
            oglVersion = matcher.group(1) + matcher.group(2)
            matcher = null
        }
    } catch (e) {
        println "Failed to extract OpenGL version: ${e.message}"
    }

    // Set OpenGL version if extracted
    try {
        /* groovylint-disable-next-line UnnecessarySetter */
        setOpenGl(oglVersion)
    } catch (e) {
        println "Failed to set OpenGL version: ${e.message}"
    }

    try {
        stage('download CTS_32 tool') {
            dir("${env.WORKSPACE}\\cts32") {
                bat '''
                    wget -q --no-check-certificate https://oss.mthreads.com/product-release/cts/gl/opengl-cts-*******-windows-release-32bit.7z
                    7z x opengl-cts-*******-windows-release-32bit.7z -aoa
                '''
            }
        }
        stage('Run CTS_32') {
            // run CTS_32 test
            dir("${env.WORKSPACE}\\cts32\\opengl-cts-*******-windows-release-32bit") {
                def test_cmd
                test_cmd = ".\\${executable} --deqp-caselist-file=${case_file}"
                test_result = bat(script: "${test_cmd}", returnStdout: true).trim()
                echo "${test_result}"

                //check result log
                if (checkPassRate(test_result)) {
                    println 'Test passed'
                } else {
                    error 'test failed!'
                }
            }
        }
    } catch (e) {
        error "Error during test execution: ${e.message}"
    } finally {
        setOpenGl()
    }
}

def ogl_oglTest_test(String caselist = 'passlist-oglTest.txt', String oglTestCommitId = env.ogltest) {
    def baseDir = caselist.contains('vps') ? 'C:\\win_test' : 'D:\\win_test'
    def case_file = "${baseDir}\\ogltests\\passlists\\${caselist}"

    dir(baseDir) {
        fetchTestRepo('ogltests', 'master', oglTestCommitId)
    }

    try {
        stage('Run oglTest') {
            dir("${baseDir}\\ogltests") {
                def dependency = 'ogltests'
                def build_dir = 'build'
                if (fileExists('oglTest\\get_ogltests_results.py')) {
                    dependency = 'ogltests/oglTest'
                    build_dir = 'oglTest\\build'
                }
                dir("${baseDir}\\ogltests\\${build_dir}") {
                    bat '''
                        cmake ..
                        cd ..
                        cmake --build ./build --config Release
                    '''
                }
                dir("${baseDir}\\ogltests\\${build_dir}\\Release") {
                    bat """
                        cp -rf ${baseDir}/${dependency}/golden .
                        cp -rf ${baseDir}/${dependency}/resource .
                        cp -rf ${baseDir}/${dependency}/get_ogltests_results.py .
                        cp -rf ${env.WORKSPACE}/driver/mticdg64.dll .
                    """
                    def ogl_result = bat(script: "python get_ogltests_results.py -l ${case_file}", returnStdout: true).trim()
                    println ogl_result
                    if (checkPassRate(ogl_result)) {
                        println 'Test passed'
                    } else {
                        error 'test failed!'
                    }
                }
            }
        }
    } catch (e) {
        error "Error occurred! Please check: ${e.message}"
    }
}

def sdkGoogle_test() {
    def types = [
        '_Debug-Win32.tar.gz',
        '_Debug-Win64.tar.gz',
        '_Release-Win32.tar.gz',
        '_Release-Win64.tar.gz'
    ]

    types.each { suffix ->
        def url = "${env.sdkGoogle_test_url}${suffix}"
        def pkgName = url.split('/')[-1]
        def extractDir = "sdk_googletest_${suffix.replace('.tar.gz', '').replace('_', '')}"
        bat """
            wget -q ${url} -O ${pkgName} --no-check-certificate
            md ${extractDir}
            tar xvzf ${pkgName} -C ${extractDir}
            cd ${extractDir} && sdk_googletest.exe -p=2
        """
    }
}

def vulkan_cts_test(String testcaseFilename, String vkExeUrl = env.vkExeUrl) {
    def vkExeDir = 'D:\\vulkan-cts'
    def vkExeArchive = 'vulkan-cts-latest-win64.7z'
    def foundVkExePath = ''

    if (fileExists(vkExeDir)) {
        bat "rd /s /q \"${vkExeDir}\""
    }
    bat "md \"${vkExeDir}\""

    dir(vkExeDir) {
        bat """
            wget -q --no-check-certificate "${vkExeUrl}" -O "${vkExeArchive}"
            7z x "${vkExeArchive}" -aoa
        """
        def files = findFiles(glob: '**/deqp-vk.exe')
        if (files.size() > 0) {
            foundVkExePath = files[0].path.replace('/', '\\')
            echo "Found deqp-vk.exe at: ${foundVkExePath}"
        } else {
            error "deqp-vk.exe not found in ${vkExeDir} after extraction."
        }
    }

    def caseFile = "${env.WORKSPACE}\\driver\\${testcaseFilename}"
    def exeDir = foundVkExePath.substring(0, foundVkExePath.lastIndexOf('\\'))
    def exeName = foundVkExePath.substring(foundVkExePath.lastIndexOf('\\') + 1)

    bat """
        cd /d "${vkExeDir}\\${exeDir}"
        ${exeName} --deqp-caselist-file="${caseFile}"
    """
}
