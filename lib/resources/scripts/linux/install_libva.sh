#!/bin/bash
LIBVA_DIR=$1
driver_path=$2
libva_version=${3:-"2.15"}

echo " install libva"
if [ ! -d $LIBVA_DIR ];then
    export LIBVA_DRIVER_NAME=mtgpu
    export LIBVA_DRIVERS_PATH="${driver_path}"
    set +e
    vainfo
    #   if [ $? -eq 0 ];then
    #       echo "installed libva"
    #       set -e
    #   else
    set -e
    sudo apt install lcov -y
    sudo apt-get -y install autoconf automake build-essential libass-dev libtool pkg-config \
    texinfo zlib1g-dev libva-dev cmake mercurial libdrm-dev libvorbis-dev libogg-dev git \
    libx11-dev libperl-dev libpciaccess-dev libpciaccess0 xorg-dev git-core libfreetype6-dev \
    libtool libvdpau-dev libxcb1-dev libxcb-shm0-dev libxcb-xfixes0-dev wget zlib1g-dev
    wget https://oss.mthreads.com/release-ci/media/depend/libva-${libva_version}.zip --no-check-certificate
    unzip libva-${libva_version}.zip;rm -f libva-${libva_version}.zip;cd libva-${libva_version};chmod 755 autogen.sh;./autogen.sh;./configure;make -j12
    rm -f /usr/local/lib/libva*;rm -rf /usr/local/include/va; sudo make install
    ldconfig
    sudo apt install vainfo -y
    cd ..
    #   fi
else
    cd libva-${libva_version};chmod 755 autogen.sh;./autogen.sh;./configure;make -j12
    rm -f /usr/local/lib/libva*;rm -rf /usr/local/include/va;sudo make install
    cd ..
fi
