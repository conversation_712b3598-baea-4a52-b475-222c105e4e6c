default-trigger-phrase: &default-trigger-phrase (?i)cirerun|(?i)cirenew||(?i)aipass
default-triggers: # job with not triggers below will use this trigger
  - gitlabActionType: MERGE
    gitlabMergeRequestState: opened
    gitlabTargetBranch: master|develop|^release.*
  - gitlabActionType: NOTE
    gitlabTriggerPhrase: *default-trigger-phrase
    gitlabTargetBranch: master|develop|^release.*
gr-kmd: # this is gitlab project name
  - name: mr.gr-kmd
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: develop|^[rR]elease(?!.*vgpu).*|musa_3.1.1_hygon_kylin
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: (?i)cirerun|(?i)cirenew|(?i)runtest
        gitlabTargetBranch: develop|^[rR]elease(?!.*vgpu).*|musa_3.1.1_hygon_kylin
  - name: push.gr-kmd
    triggers: # overwrite default-triggers
      - gitlabActionType: PUSH
        gitlabTargetBranch: master|develop|^[rR]elease.*
  # - name: mr.linux_vdi
  #   debug: "false"
  #   triggers:
  #     - gitlabActionType: MERGE
  #       gitlabMergeRequestState: opened
  #       gitlabTargetBranch: develop
  #     - gitlabActionType: NOTE
  #       gitlabTriggerPhrase: (?i)cirerun|(?i)cirenew|(?i)runtest
  #       gitlabTargetBranch: develop
  # - name: push.linux_vdi
  #   triggers:
  #     - gitlabActionType: PUSH
  #       gitlabTargetBranch: develop
  - name: win-vdi-build
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: develop|release_vgpu_.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: (?i)cirerun|(?i)cirenew|(?i)runtest
        gitlabTargetBranch: develop|release_vgpu_.*
  - name: mr.gr-kmd-test
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^ciTest1$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: (?i)cirerun|(?i)runtest|(?i)cirenew
        gitlabTargetBranch: ^ciTest1$
gr-umd:
  - name: mr.gr-umd
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: develop|^[rR]elease(?!.*vgpu).*|dev-nova
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: develop|^[rR]elease(?!.*vgpu).*|dev-nova
  # - name: mr.linux_vdi
  #   debug: "false"
  #   triggers:
  #     - gitlabActionType: MERGE
  #       gitlabMergeRequestState: opened
  #       gitlabTargetBranch: develop
  #     - gitlabActionType: NOTE
  #       gitlabTriggerPhrase: (?i)cirerun|(?i)cirenew|(?i)runtest
  #       gitlabTargetBranch: develop
  # - name: push.linux_vdi
  #   triggers:
  #     - gitlabActionType: PUSH
  #       gitlabTargetBranch: develop
  - name: push.gr-umd
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: develop|^release.*
  - name: ACX_build_driver
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: develop
m3d_cts:
  - name: mr.m3d_cts
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: develop.hal.m3d|develop|^master$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: develop.hal.m3d|develop|^master$
MUSA-Runtime:
  - name: mr.MUSA-Runtime
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: develop.hal.m3d|develop|^master$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: develop.hal.m3d|develop|^master$|^release.*
  - name: push.MUSA-Runtime
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$
musa:
  - name: mr.musa
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: develop.hal.m3d|develop|^master$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: develop.hal.m3d|develop|^master$|^release.*
libdrm-mt:
  - name: mr.libdrm-mt
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: (?i)cirerun|(?i)cirenew|(?i)runtest
        gitlabTargetBranch: ^master$|^release.*
  - name: push.libdrm-mt
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: master|develop|^release.*
SWQA-CI:
  - name: mr.swqa-ci
    debug: "false"
  # - name: ai_review
  #   debug: "false"
m3d:
  - name: mr.m3d
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master|^release.*
  - name: mr.m3d.win
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master
mtcc:
  - name: mr.mtcc
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master|^release.*|windows_10.0_release|PH1S_SLT_v0.1
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master|^release.*|windows_10.0_release|PH1S_SLT_v0.1
  - name: push.mtcc
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: master|^release.*
  - name: mr.mtcc.win
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master
  - name: push.mtcc.win
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: master

wddm:
  - name: mr.wddm.win
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$|windows_12.0_release
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$|windows_12.0_release
  - name: push.wddm.win
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^develop$|windows_12.0_release
  - name: mr.wddm.release11
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: windows_11.0_release
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: windows_11.0_release
  - name: win-vdi-guest270
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: release_vgpu_2.7.0
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: release_vgpu_2.7.0
  - name: mr.vdi.release256
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: release_vgpu_2.5.6
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: release_vgpu_2.5.6
  - name: mr.vdi.release275
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: release_vgpu_2.7.5
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: release_vgpu_2.7.5
mtkmd:
  - name: mr.mtkmd.win
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^release.*|
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^release.*|
ogl:
  - name: mr.ogl
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^release.*|gles_newapi_develop
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^release.*|gles_newapi_develop
  - name: mr.ogl.win
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$
  - name: push.ogl
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$|^release.*
  - name: push.ogl.win
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$

linux-ddk:
  - name: mr.linux-ddk
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^release.*|PH1S_SLT_v0.1|kuae_axi_decode_debug
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^release.*|PH1S_SLT_v0.1|kuae_axi_decode_debug
  - name: push.linux-ddk
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$|^release.*|PH1S_SLT_v0.1

mt-vgpu:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$
  - name: mr.mt-vgpu.win
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$
  - name: push.mt-vgpu.win
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$

mt-tools-extension:
  - name: mr.mt-tools-extension
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master|develop|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master|develop|^release.*
  - name: push.mt-tools-extension
    triggers: # overwrite default-triggers
      - gitlabActionType: PUSH
        gitlabTargetBranch: master|develop|^[rR]elease.*

mt-management:
  - name: mr.mt-management
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master|develop|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master|develop|^release.*
  - name: mr.mt-management.win
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master|develop|^[rR]elease.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master|develop|^[rR]elease.*
  - name: push.mt-management.win
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: master|develop|^[rR]elease.*
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$|^develop$|^dcgm_private_branch$|^[rR]elease.*

gpu-fw:
  - name: mr.gpu-fw
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: develop|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: develop|^release.*
  - name: mr.gpu-fw.win
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: develop|windows_10.0_release
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: develop|windows_10.0_release

mt-media-driver:
  - name: mr.mt-media-driver
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: develop|zjf/m3d|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: develop|zjf/m3d|^release.*
  # - name: mr.linux_vdi
  #   debug: "false"
  #   triggers:
  #     - gitlabActionType: MERGE
  #       gitlabMergeRequestState: opened
  #       gitlabTargetBranch: develop
  #     - gitlabActionType: NOTE
  #       gitlabTriggerPhrase: (?i)cirerun|(?i)cirenew|(?i)runtest
  #       gitlabTargetBranch: develop
  # - name: push.linux_vdi
  #   triggers:
  #     - gitlabActionType: PUSH
  #       gitlabTargetBranch: develop

mtdxum:
  - name: mr.mtdxum.win
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master

dxc:
  - name: mr.dxc.win
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master
  - name: push.dxc.win
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$

mt-video-drv:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^vgpu_2.6.5_release$|^[rR]elease.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^vgpu_2.6.5_release$|^[rR]elease.*
  - name: mr.mt-video-drv.win
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^windows_\d+\.\d+_release$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^windows_\d+\.\d+_release$

musa_cts:
  - name: mr.musa_cts
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: m3d_master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: m3d_master

vgpu_daemon:
  - name: mr.vgpu_daemon
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^vgpu_2.6.5_release$|^master$|^release_vgpu.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^vgpu_2.6.5_release$|^master$|^release_vgpu.*
  - name: push.vgpu_daemon
    debug: "true"
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^vgpu_2.6.5_release$|^master$|^release_vgpu.*

mtgpu_snapshot:
  - name: mr.mtgpu_snapshot
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|release_vgpu_2.7.0
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|release_vgpu_2.7.0
  - name: push.mtgpu_snapshot
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$|release_vgpu_2.7.0

mtvgm:
  - name: mr.mtvgm
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^release_vgpu.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^release_vgpu.*
  - name: push.mtvgm
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$|^release_vgpu.*

msight-graphics:
  - name: mr.msight-graphics
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$

mtapi:
  - name: mr.mtapi.win
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$
  - name: push.mtapi.win
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^develop$

DirectStream:
  - name: mr.DirectStream
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$|^vgpu_2.6.5_release$|^release-3.0.0$|^release_M1000.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$|^vgpu_2.6.5_release$|^release-3.0.0$|^release_M1000.*
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^develop$|^[rR]elease.*
  - name: mr.DirectStream.win
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$
  - name: push.DirectStream.win
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^develop$|^windows_\d+\.\d+_release$

mthreads-gmi:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$|^master$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$|^master$|^release.*
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^develop$|^master$|^release.*

msight-system:
  - name: mr.msight-system
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: develop|[rR]elease.*|master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: develop|[rR]elease.*|master

mutlass:
  - name: mr.mutlass
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: develop
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: develop

MUPTI:
  - name: mr.MUPTI
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master

mt-pfm-controller:
  - name: mr.mt-pfm-controller
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^develop$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^develop$|^release.*
  - name: push.mt-pfm-controller
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$|^develop$|^release.*

muBLAS:
  - name: mr.muBLAS
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$|^release.*
  - name: push.muBLAS
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: develop

muBLASLt:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$|^release.*

muBLAS_cts:
  - name: mr.muBLAS_cts
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master

muFFT:
  - name: mr.muFFT
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$|^release.*

muFFT_cts:
  - name: mr.muFFT_cts
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^release.*

muPP:
  - name: mr.muPP
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$|^release.*

muPP_cts:
  - name: mr.muPP_cts
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^release.*

muSPARSE:
  - name: mr.muSPARSE
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$|^release.*

muSPARSE_cts:
  - name: mr.muSPARSE_cts
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^release.*

muRAND:
  - name: mr.muRAND
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$|^release.*

muRAND_cts:
  - name: mr.muRAND_cts
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^release.*

muSOLVER:
  - name: mr.muSOLVER
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$|^release.*

muSOLVER_cts:
  - name: mr.muSOLVER_cts
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^release.*

Vulkan:
  - name: mr.Vulkan
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master
  - name: mr.Vulkan.win
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master

muDNN:
  - name: mr.muDNN
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^develop$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^develop$|^release.*

ComputeAsmKern:
  - name: mr.ComputeAsmKern
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master

musa_asm:
  - name: mr.musa_asm
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master
  - name: push.musa_asm
    triggers: # overwrite default-triggers
      - gitlabActionType: PUSH
        gitlabTargetBranch: master

mt-rm:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$
  - name: mr.mt-rm.win
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$

shared_include:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^release.*
  - name: mr.shared_include.win
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$

FFmpeg:
  - name: mr.FFmpeg
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^mt-7.0.2$|^nda-mt-7.0.2$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^mt-7.0.2$|^nda-mt-7.0.2$
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$|^mt-7.0.2$|^nda-mt-7.0.2$
  - name: mr.FFmpeg.win
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^mt-7.0.2$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^mt-7.0.2$
  - name: push.FFmpeg.win
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$

libva:
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$

mt-libva:
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^2.12.mt-ext$

mtcc_test:
  - name: mr.mtcc_test
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master

triton_musa:
  - name: mr.triton_musa
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: main
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: main

FlagGems:
  - name: mr.FlagGems
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master

mt-dcgm:
  - name: mr.mt-dcgm
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master|develop|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master|develop|^release.*

mt-pes:
  - name: mr.mt-pes
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: develop
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: develop
  - name: mr.mt-pes.win
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: develop
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: develop

mt-management-platform:
  - name: mr.mt-management-platform
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master|develop
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master|develop

soc-yocto-meta:
  - name: mr.soc-yocto-meta
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master|^[rR]elease.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master|^[rR]elease.*

scp-firmware:
  - name: mr.scp-firmware
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: v2.11.0@mt|^[rR]elease.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: v2.11.0@mt|^[rR]elease.*

mthreads-efi:
  - name: mr.mthreads-efi
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master|^[rR]elease.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master|^[rR]elease.*

apollo-entry-table-tool:
  - name: mr.apollo-entry-table-tool
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master|^[rR]elease.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master|^[rR]elease.*

trusted-firmware-a:
  - name: mr.trusted-firmware-a
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: v2.8.0@mt|^[rR]elease.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: v2.8.0@mt|^[rR]elease.*

pipewire:
  - name: mr.pipewire
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^pw_.*|^[rR]elease.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^pw_.*|^[rR]elease.*
  - name: push.pipewire
    triggers: # overwrite default-triggers
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^pw_.*|^[rR]elease.*

media-session:
  - name: mr.media-session
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master|^[rR]elease.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master|^[rR]elease.*
  - name: push.media-session
    triggers: # overwrite default-triggers
      - gitlabActionType: PUSH
        gitlabTargetBranch: master|^[rR]elease.*

wireplumber:
  - name: mr.wireplumber
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: *******|^[rR]elease.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: *******|^[rR]elease.*
  - name: push.wireplumber
    triggers: # overwrite default-triggers
      - gitlabActionType: PUSH
        gitlabTargetBranch: *******|^[rR]elease.*

msight-compute:
  - name: mr.msight-compute
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master|develop|^[rR]elease.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master|develop|^[rR]elease.*
  - name: push.msight-compute
    triggers: # overwrite default-triggers
      - gitlabActionType: PUSH
        gitlabTargetBranch: master|develop|^[rR]elease.*

wave627_fw:
  - name: mr.mm_fw
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$

wave517_fw:
  - name: mr.mm_fw
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$

mt-audio-windows-driver:
  - name: mr.mt-audio-windows-driver
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$
  - name: push.mt-audio-windows-driver
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$

mt-audio-driver:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^develop$

mtgpu-auto-header:
  - name: mr.mtgpu-auto-header
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$

mtext:
  - name: mr.mtext
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^[rR]elease.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^[rR]elease.*

mtJPEG2000:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$|^[rR]elease.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$|^[rR]elease.*
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^develop$|^[rR]elease.*

MTJPEGSDK:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$

Qt-Installer-Framework:
  - name: mr.Qt-Installer-Framework.win
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$

dcsim:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$

********************:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|^develop$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|^develop$|^release.*
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^master$|^develop$|^release.*

mccl:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|develop_qy2|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|develop_qy2|^release.*

mccl_cts:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^master$|qingnian_ph1|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^master$|qingnian_ph1|^release.*

mthreads-smi:
  - name: mr.scheduler
    debug: "false"
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: ^develop$|^master$|^release.*
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: ^develop$|^master$|^release.*
  - name: push.scheduler
    triggers:
      - gitlabActionType: PUSH
        gitlabTargetBranch: ^develop$|^master$|^release.*

diagsys-ci:
  - name: mr.diagsys-ci.win
    triggers:
      - gitlabActionType: MERGE
        gitlabMergeRequestState: opened
        gitlabTargetBranch: master
      - gitlabActionType: NOTE
        gitlabTriggerPhrase: *default-trigger-phrase
        gitlabTargetBranch: master
