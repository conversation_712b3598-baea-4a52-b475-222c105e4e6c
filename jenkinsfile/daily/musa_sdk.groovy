@Library('swqa-ci')
import org.swqa.tools.common

commonLib = new common()
/*
 * parameters
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_jump
 * cluster_build (String) - releaseFarm
 * cluster_test (String) - releaseFarm
 * podNodeSelector (String) - In=mt=buildserver 编译job的选择器
 * podResources (String) - requests=cpu=8;requests=memory=64Gi;limits=cpu=64;limits=memory=128Gi;
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_compile:v7
 * ddk_branch (String) - ''
 * OSS_SAVE_URL (String) - oss/release-ci/computeQA/cuda_compatible/CI/${ddk_branch}/${BUILD_TIMESTAMP}/
 * aoto_subfolder (boolean) - true
 * ciConfig (String) - https://oss.mtheads.com/release_ci/computeQA/cuda_compatible/CI/{ddk_branch}/.ciConfig.yaml
*/

//value will be set by confirm_ddk()
ddk_url = ''
ddk_commit = ''
target_toolkit_name = ''
target_mtcc_name = ''
// musaRuntimePackageUrl = ''

//value will be set by adjust_ddk()
target_ddk_name = ''

//constant values
SAVE_URI = "${constants.OSS.URL_PREFIX}/${env.OSS_SAVE_URL}"
SAVE_OSS_PATH = "oss/${env.OSS_SAVE_URL}"
if (SAVE_OSS_PATH.endsWith('/')) {
    SAVE_OSS_PATH = SAVE_OSS_PATH.substring(0, SAVE_OSS_PATH.length() - 1)
}
if (SAVE_URI.endsWith('/')) {
    SAVE_URI = SAVE_URI.substring(0, SAVE_URI.length() - 1)
}

if (env.auto_subfolder == 'true') {
    BUILD_TIMESTAMP = env.BUILD_TIMESTAMP.split('_')[0]
    SAVE_URI = "${SAVE_URI}/${BUILD_TIMESTAMP}"
    SAVE_OSS_PATH = "${SAVE_OSS_PATH}/${BUILD_TIMESTAMP}"
}

global_build_musa_toolkit = [
    'cluster': env.cluster_build,
    'podNodeSelector': env.podNodeSelector,
    'podResources': env.podResources,
    'containerImage': env.containerImage,
    'OSS_SAVE_URL': "${SAVE_OSS_PATH}"
]

global_build_pkgs = [
    'cluster': env.cluster_build,
    'podNodeSelector': env.podNodeSelector,
]

/*
1. fetch latest ddk
*/
def confirm_ddk(ddk_branch) {
    def pkg_suffix = env.ddk_suffix
    ddk_commit = constants.getLatestPackageCommitId('linux-ddk', ddk_branch)

    // copy latest.txt of linux-ddk to daily build dir of oss.
    String ossPath_ddk = constants.genLatestOssPath('linux-ddk', ddk_branch)
    def ossPath_ddk_latest = "${constants.genOssAlias(ossPath_ddk)}/${ossPath_ddk}/latest.txt"
    println "ddk_latest: ${ossPath_ddk_latest}"
    oss.cp(ossPath_ddk_latest, "${SAVE_OSS_PATH}")

    def ddk_url_prefix = constants.genOssPath('linux-ddk', ddk_branch, ddk_commit)
    ddk_url = "oss/${ddk_url_prefix}/${ddk_commit}_${pkg_suffix}"
    target_toolkit_name = "${ddk_commit}_musa_toolkits_install_full.tar.gz"
    target_mtcc_name = 'mtcc-x86_64-linux-gnu-ubuntu.tar.gz'
    println "ddk_url: ${ddk_url}"

// if (ddk_branch == 'master') {
//     musaRuntimePackageUrl = constants.genLatestPackageUrl('MUSA-Runtime', 'master', 'musaRuntime.tar.gz')
//     println "musaRuntimePackageUrl: ${musaRuntimePackageUrl}"
//     musaRuntimePackageOss = constants.urlToOSSPath(musaRuntimePackageUrl)
//     println "musaRuntimePackageOss: ${musaRuntimePackageOss}"
//     oss.cp(musaRuntimePackageOss, "${SAVE_OSS_PATH}")
// }
}

def display_info() {
    // 计算目标URL
    def linuxDdkPackageUrl = "${SAVE_URI}/${target_ddk_name}"
    def musaToolkitsPackageUrl = "${SAVE_URI}/${target_toolkit_name}"
    def mtccPackageUrl = "${SAVE_URI}/${target_mtcc_name}"
    // 设置Job描述
    currentBuild.description = """
    <p><b>cluster_test:</b> ${env.cluster_test}</p>
    <p><b>linuxDdkBranch:</b> ${env.ddk_branch}</p>
    <p><b>linuxDdkCommitId:</b> ${ddk_commit}</p>
    <p><b>linuxDdkPackageUrl:</b> ${linuxDdkPackageUrl}</p>
    <p><b>musaToolkitsPackageUrl:</b> ${musaToolkitsPackageUrl}</p>
    <p><b>mtccPackageUrl:</b> ${mtccPackageUrl}</p>
    """
}

def build_musa_toolkit(config) {
    def buildTasks = [:]
    def builds = config.build_toolkits
    for (buildConfig in builds) {
        Map _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            ddk_url: ddk_url,
            ddk_commit_id: ddk_commit,
            packagePath: "${SAVE_OSS_PATH}"
        ]
        global_build_musa_toolkit.each { key, value ->
            parameters[key] = value
        }
        defaultParameters.each { key, value ->
            parameters[key] = value
            if (key == 'rename_pkg_musa_toolkits') {
                target_toolkit_name = value
            }
            if (key == 'rename_pkg') {
                target_mtcc_name = value
            }
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

def adjust_ddk(ddk_branch) {
    oss.cp(ddk_url)
    if (ddk_url.endsWith('.rpm')) {
        target_ddk_name = ddk_url.split('/')[-1]
    } else {
        sh '''
            rm *.asan.deb ||:
        '''
        def DEB_VERSION = env.DDK_VERSION ?: sh(script: "dpkg-deb -I *.deb|grep 'Version:'|awk '{print \$2}'", returnStdout: true).trim()
        sh '''
            pwd && ls -l
            dpkg-deb -R *_ddk2.0.deb ddk_2_deb
            rm -rf ddk_2_deb/home ||:
        '''

        if (ddk_branch == 'release_KUAE_2.0_for_PH1_M3D' || ddk_branch == 'release_musa_4.0.0') {
            sh """
                sed -i 's/mtgpu_drm_major=2/compute_only=1/g' ddk_2_deb/DEBIAN/preinst
            """
        }

        sh """
            chmod 755 ddk_2_deb/DEBIAN/preinst
            chmod 755 ddk_2_deb/DEBIAN/postinst
            chmod 755 ddk_2_deb/DEBIAN/prerm
            chmod 755 ddk_2_deb/DEBIAN/postrm
            dpkg-deb --build ddk_2_deb/ musa_${DEB_VERSION}_amd64.deb
        """
        target_ddk_name = "musa_${DEB_VERSION}_amd64.deb"
    }
    oss.cp(target_ddk_name, "${SAVE_OSS_PATH}")
}

def build(config) {
    def buildTasks = [:]
    def modules = config.build_modules
    for (moduleConfig in modules) {
        Map _moduleConfig = moduleConfig
        def defaultParameters = _moduleConfig.parameters ?: [:]
        def parameters = [
            packagePath: "${SAVE_OSS_PATH}".replace('oss/', ''),
            musaToolkitsPackageUrl: "${SAVE_URI}/${target_toolkit_name}",
            linuxDdkPackageUrl: "${SAVE_URI}/${target_ddk_name}"
        ]
        global_build_pkgs.each { key, value ->
            parameters[key] = value
        }
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_moduleConfig.name ?: _moduleConfig.job}"] = {
            runPipeline.runJob([
                job: "${_moduleConfig.job}",
                parameters: parameters
            ])
        }
    }

    def others = config.build_others
    for (moduleConfig in others) {
        Map _moduleConfig = moduleConfig
        def defaultParameters = _moduleConfig.parameters ?: [:]
        def parameters = [
            packagePath: "${SAVE_OSS_PATH}/others",
            musaToolkitsPackageUrl: "${SAVE_URI}/${target_toolkit_name}",
            linuxDdkPackageUrl: "${SAVE_URI}/${target_ddk_name}"
        ]
        global_build_pkgs.each { key, value ->
            parameters[key] = value
        }
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_moduleConfig.name ?: _moduleConfig.job}"] = {
            runPipeline.runJob([
                job: "${_moduleConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

def deploy(config) {
    def deployTasks = [:]
    def deploys = config.deploys
    for (deployConfig in deploys) {
        Map _deployConfig = deployConfig
        def defaultParameters = _deployConfig.parameters ?: [:]
        def parameters = [
            mtmlPackageUrl: env.mtmlPackageUrl,
            linuxDdkPackageUrl: "${SAVE_URI}/${target_ddk_name}",
            context: env.cluster_test.toLowerCase(),
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        deployTasks["${_deployConfig.name ?: _deployConfig.job}"] = {
            runPipeline.runJob([
                job: "${_deployConfig.job}",
                parameters: parameters
            ])
        }
    }
    println "deployTasks: ${deployTasks}"
    parallel deployTasks
}

def test(tests_group) {
    def testTasks = [:]
    def tests = tests_group
    // 生成统一的基础路径
    def reportOssPath = "${SAVE_OSS_PATH}/driver_toolkits_test"

    for (testConfig in tests) {
        Map _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def parameters = [
            cluster: env.cluster_test,
            ddkBranch: ddk_branch,
            ddkCommitId: ddk_commit,
            musaToolkitsPackageUrl: "${SAVE_URI}/${target_toolkit_name}",
        // linuxDdkPackageUrl: "${SAVE_URI}/${target_ddk_name}"
        ]

        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        if (ddk_branch == 'master') {
            parameters['mtccPackageUrl'] = "${SAVE_URI}/${target_mtcc_name}"
        }

        // 从podNodeSelector中提取GPU_TYPE
        def gpuType = 'unknown'
        if (defaultParameters.containsKey('podNodeSelector')) {
            def selector = defaultParameters['podNodeSelector']
            def matcher = selector =~ /GPU_TYPE=(\w+)/
            if (matcher.find()) {
                gpuType = matcher.group(1)
            }
            println "gpuType: ${gpuType}"
        }

        // 生成最终的报告路径
        parameters['reportOssPath'] = "${reportOssPath}/${gpuType}/"
        println "reportOssPath: ${parameters['reportOssPath']}"
        testTasks["${_testConfig.name ?: _testConfig.job}"] = {
            runPipeline.runJob([
                job: "${_testConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel testTasks
}

runner.start(env.runChoice) {
    def config = null

    oss.install()
    constants.downloadPackage(env.ciConfig)
    yaml_file = "${env.ciConfig}".split('/')[-1]
    config = commonLib.loadPipelineConfig("${yaml_file}", '')
    def workflow = [:]
    workflow['confirm_ddk'] = [ closure: { confirm_ddk(env.ddk_branch) } ]
    workflow['build_musa_toolkit'] = [ closure: { build_musa_toolkit(config) }]
    workflow['adjust_ddk'] = [ closure: { adjust_ddk(env.ddk_branch) } ]
    workflow['display_info'] = [ closure: { display_info() } ]
    workflow['build_pkgs'] = [ closure: {
        catchError(stageResult: 'FAILURE') {
            build(config)
        }
    }]
    workflow['deploy'] = [ closure: { deploy(config) }]
    workflow['test'] = [ closure: { test(config.tests) }]
    workflow['tests_exception'] = [ closure: { test(config.tests_exception) }]
    runPipeline(workflow)
}
