@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

/*
 * parameters
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_build
 * cluster (String) - shfarm
 * podNodeSelector (String) - In=mt=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=64Gi;limits=cpu=64;limits=memory=128Gi;
 * containerImage (String) - sh-harbor.mthreads.com/qa/m3d_test:v10
 * musa_cts_branch (String) - 'm3d_master'
 * linuxDdkPackageUrl (String) - ''
 * musaToolkitsPackageUrl (String) - ''
 * envExport (String) - ''
 * compileParallelNumber (int) - 12
 * compileArgs (String) - ''
 * musaAsmPackageUrl (String) - 'https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/newest/master/musa_asm.tar.gz'
 * work_dir (String) - 'pytest'
 * test_cmd (String) - ''
 * gpuArch (String) - 'mp_31'
*/

gitLib = new git()
commonLib = new common()
musaCtsRepo = 'musa_cts'

def fetchCode() {
    gitLib.fetchCode(musaCtsRepo, env.musa_cts_branch)
}

def setupDepends() {
    if (env.setup_ddk == 'true' && env.linuxDdkPackageUrl) {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }

    musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    if (env.muAlgPackageUrl) {
        musa.installmuAlg(env.muAlgPackageUrl)
    }

    if (env.muThrustPackageUrl) {
        musa.installmuThrust(env.muThrustPackageUrl)
    }

    dir(musaCtsRepo) {
        if (env.gpuArch == 'mp_31') {
            constants.downloadAndUnzipPackage(env.musaAsmPackageUrl)
            sh '''
                cd musa_asm/build/bin
                cp musaasm /usr/local/musa/bin/
            '''

            sh """
                wget --no-check-certificate ${env.mtcToolUrl} -O mtc_tool
                chmod +x mtc_tool
                cp mtc_tool /usr/local/bin/
            """
        }
    }
}

def build() {
    dir(musaCtsRepo) {
        sh """
            export PATH=/usr/local/musa/bin:\$PATH
            export LD_LIBRARY_PATH=/usr/local/musa/lib:\$LD_LIBRARY_PATH
            ${env.envExport}
            make porting-cuda-to-musa
            make build-all
        """
    }
}

def runCtsTest() {
    timeout(time: env.testTimer, unit: 'MINUTES') {
        dir(musaCtsRepo) {
            sh """
                export PATH=/usr/local/musa/bin:\$PATH
                export LD_LIBRARY_PATH=/usr/local/musa/lib:\$LD_LIBRARY_PATH;
                ${env.envExport}
                cd ${env.work_dir}
                ${test_cmd} --alluredir=allure_result_musa_cts ||:
                tar -czf m3d_musa_cts_allure_result.tar.gz allure_result_musa_cts/
            """
        }
    }
}

def checkResult() {
    dir(musaCtsRepo) {
        commonLib.allure("${env.work_dir}/allure_result_musa_cts")
        if (currentBuild.result == 'UNSTABLE') { error "Test failed, please check: ${allureReportUrl}" }
    }
}

runner.start(env.runChoice) {
    def workflow = [:]
    workflow.fetchCode = [closure: { fetchCode() }]
    workflow.setupDepends = [closure: { setupDepends() }]
    workflow.build = [closure: { build() }]
    workflow.run = [closure: { runCtsTest() }]
    workflow.check = [closure: { checkResult() }]
    runPipeline(workflow)
}
