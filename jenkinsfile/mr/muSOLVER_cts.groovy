@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

commonLib = new common()
gitLib = new git()

env.repo = 'muSOLVER_cts'
mtccCommitIdMap = [:]

def build(config) {
    def buildTasks = [:]
    def builds = config.builds
    for (buildConfig in builds) {
        def _buildConfig = buildConfig
        def defaultParameters = _buildConfig.parameters ?: [:]
        def parameters = [
            triggerInfo: env.triggerInfo,
            musaRuntimePackageUrl: constants.genLatestPackageUrl('MUSA-Runtime', 'master', 'musaRuntime.tar.gz'),
            muBLASPackageUrl: constants.genLatestPackageUrl('muBLAS', 'develop', 'muBLAS.tar.gz'),
            linuxDdkPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/musa_Ubuntu_amd64.deb",
            mtccPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/mtcc-nightly-x86_64-linux-gnu-ubuntu-22.04.tar.gz"
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        buildTasks["${_buildConfig.name ?: _buildConfig.job}"] = {
            runPipeline.runJob([
                job: "${_buildConfig.job}",
                parameters: parameters
            ])
        }
    }
    parallel buildTasks
}

runner.start(env.runChoice) {
    def config = null
    stage('checkout') {
        gitLib.fetchCode(repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true])
        dir(repo) {
            config = commonLib.loadPipelineConfig('.ciConfig.yaml', "muSOLVER_cts/${env.gitlabTargetBranch}.yaml", [
            gitlabMergeRequestIid: env.gitlabMergeRequestIid,
            BUILD_ID: env.build_ID,
            commitId: env.gitlabMergeRequestLastCommit])
        }
    }
    mtccCommitIdMap = constants.getCommitIdfromciConfig(config, 'mtccBranch')
    def workflow = [:]
    if (config.builds && config.builds.size() > 0) {
        workflow['build'] = [ closure: { build(config) }, setGitlabStatus: true ]
    }
    def tests = config.tests ?: []
    for (testConfig in tests) {
        def _testConfig = testConfig
        def defaultParameters = _testConfig.parameters ?: [:]
        def name = _testConfig.name ?: _testConfig.job
        def parameters = [
            branch: env.gitlabSourceBranch,
            commitId: env.gitlabMergeRequestLastCommit,
            testLabel: name,
            triggerInfo: env.triggerInfo,
            musaRuntimePackageUrl: constants.genLatestPackageUrl('MUSA-Runtime', 'master', 'musaRuntime.tar.gz'),
            linuxDdkPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/musa_Ubuntu_amd64.deb",
            mtccPackageUrl: "https://oss.mthreads.com/release-ci/computeQA/cuda_compatible/CI/stable/${defaultParameters.linuxDdkBranch}/mtcc-nightly-x86_64-linux-gnu-ubuntu-22.04.tar.gz",
            muSOLVERPackageUrl: constants.genPackageUrl(env.repo, env.gitlabSourceBranch, env.gitlabMergeRequestLastCommit, defaultParameters.packageName ?: 'muSOLVER.tar.gz'),
            muBLASPackageUrl: constants.genLatestPackageUrl('muBLAS', 'develop', 'muBLAS.tar.gz')
        ]
        defaultParameters.each { key, value ->
            parameters[key] = value
        }
        workflow["${name}"] = [
            job: _testConfig.job,
            parameters: parameters,
            setGitlabStatus: true,
            statusName: parameters['testLabel'],
            async: true
        ]
    }

    runPipeline(workflow)
}
