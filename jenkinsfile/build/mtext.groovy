@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

def fetchCode() {
    gitLib.fetchCode('mtext', env.branch, env.commitId)
    if (env.wddmCommitId) {
        gitLib.fetchCode('wddm', null, env.wddmCommitId, [disableSubmodules: true])
        dir('wddm') {
            utils.runCommand('git submodule update --init shared_include')
        }
        bat '''
            rm -rf mtext/shared_include
            cp -rf wddm/shared_include mtext
        '''
    }
}

def runBuild() {
    dir('mtext') {
        // bat 'vs_build.bat'
        utils.runCommand(env.cmd)
    }
}

runner.start(env.runChoice) {
    runPipeline([
        'checkout': [ closure: { fetchCode() } ],
        'build': [ closure: { runBuild() } ]
    ])
}
