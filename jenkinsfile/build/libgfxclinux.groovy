@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch (String) - master
 * commitId (String) - ''
 * linuxDdkBranch (String) - master
 * linuxDdkCommitId (String) - ''
 * packageName (String) - libgfxc.tar.gz
 * exports (Multiline String) default '', split by '\n'
 * compileArgs (String) --build_type release
 * compileParallel (String) -16
 * litUtTest (Choice) - 'false'
 * linuxDdkPackageUrl (String) - ''
 * packagePath(String) - ''
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_compile:v1
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=2;requests=memory=5Gi;limits=cpu=10;limits=memory=25Gi
*/

env.repo = 'mtcc'
env.ccachePath = '/home/<USER>/libgfxc_ccache'

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    env.linuxDdkCommitId = gitLib.fetchCode('linux-ddk', env.linuxDdkBranch, env.linuxDdkCommitId)
    env.shared_include_path = "${env.WORKSPACE}/linux-ddk/shared_include"
    linuxDdkPackageName = "${env.linuxDdkCommitId}_${env.linuxDdkPackageName}"
}

def buildlibgfxc() {
    envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    if (isUnix()) {
        def ccachedCmd = new common().ccachedCmd(env.ccachePath, '50G')
        new common().retryByRandomTime({
            sh """
                apt install -y libncurses5
                ${ccachedCmd}
                ${envExport}
                export PATH=$PATH:${env.WORKSPACE}/mtcc/libgfxc/gfx/Linux/musa_compiler64_shared/bin
                export LD_LIBRARY_PATH=${env.WORKSPACE}/mtcc/libgfxc/gfx/Linux/musa_compiler64_shared/lib
            """
        }, 20)
    }
    credentials.runWithCredential('SSH_GITLAB') {
        dir(env.repo) {
            sh """
                cd libgfxc
                ${env.libgfxcBuildCmd} -s ${env.shared_include_path} --ccache --ccache_dir ${ccachePath}
            """
        }
    }
}

def buildlinuxDdk() {
    dir('linux-ddk') {
        sh """
            rm -rf ./libgfxc/Linux/musa_compiler_shared.tar.gz ./libgfxc/Linux_Release/musa_compiler_shared.tar.gz
            cp -r ${env.WORKSPACE}/${env.repo}/libgfxc/gfx/Linux/musa_compiler_shared.tar.gz ./libgfxc/Linux/
            cp -r ${env.WORKSPACE}/${env.repo}/libgfxc/gfx/Linux_Release/musa_compiler_shared.tar.gz ./libgfxc/Linux_Release/
        """
        sh "${env.linuxDdkCmd}"
    }
}

def updategfxc() {
    gitLib.fetchCode(env.libgfxcPakcageRepo, env.libgfxcPakcagebBranch, '', [preBuildMerge: false, lfs: true, shallow: false, updateBuildDescription: true])
    dir(env.WORKSPACE) {
        new common().retryByRandomTime({
            sh "cp -r ${env.WORKSPACE}/${env.repo}/libgfxc/gfx/* ${env.WORKSPACE}/${env.libgfxcPakcageRepo}/"
        }, 20)
    }

    credentials.runWithCredential('SSH_GITLAB') {
        dir(env.libgfxcPakcageRepo) {
            sh """
                git config --global user.email "<EMAIL>"
                git config --global user.name "xinnan.gao"
                git status
                git lfs track "*.tar.gz" || :
                git add . || :
                git commit -m "daily build mtcc:${commitId} linux-ddk:${linuxDdkCommitId}" || :
            """
            new common().retryByRandomTime({
                sh """
                git lfs push origin ${env.libgfxcPakcagebBranch} || :
                git push  origin ${env.libgfxcPakcagebBranch} || :
                """
            }, 20)
        }
    }
}

def upload() {
    commonLib.retryByRandomTime({
        sh "tar -czf ${linuxDdkPackageName} linux-ddk/"
        if (env.packagePath) {
            artifact.upload("${linuxDdkPackageName}", env.packagePath)
        } else {
            artifact.upload(env.repo, env.branch, env.commitId, "${linuxDdkPackageName}")
        }
    }, 20)
}

runner.start(env.runChoice) {
    def workflow = [
        'fetchCode': [closure: { fetchCode() }],
        'buildlibgfxc': [closure: { buildlibgfxc() }],
    ]
    if (env.updategfxc == 'true') {
        workflow['updategfxc'] = [closure: { updategfxc() }]
    }
    if (env.linuxDdkBuild == 'true') {
        workflow['buildlinuxDdk'] = [closure: { buildlinuxDdk() }]
        workflow['upload'] = [closure: { upload() }]
    }

    runPipeline(workflow)
}
