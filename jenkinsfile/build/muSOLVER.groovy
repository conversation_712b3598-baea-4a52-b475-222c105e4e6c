@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
 * exports (Multiline String) default 'usual export', split by '\n'
 * musaToolkitsPackageUrl (String) - default 'musatoolkit url'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd-uos:v26
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=9;requests=memory=96Gi;limits=cpu=18;limits=memory=96Gi
*/

env.repo = 'muSOLVER'
dependentDir = "${env.WORKSPACE}/relyPkgDir"
List envs = env.exports ? env.exports.split('\n') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''

// this pipeline can be triggered in other repos's CI
if (env.gitlabSourceRepoName == env.repo) {
    env.branch = env.gitlabSourceBranch
    env.commitId = ''
}

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    packageName = env.packagePath ? env.packageName : "${env.gitlabMergeRequestLastCommit ? constants.formatCommitID(env.gitlabMergeRequestLastCommit) : env.commitId}_${env.packageName}"
}

def envSet() {
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    }else {
        sh '''
            apt update ||:
            apt install libcunit1 -y ||:
        '''
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
        constants.downloadAndUnzipPackage(env.muBLASPackageUrl)
        sh 'cd muBLAS && chmod +x install.sh . && ./install.sh'
    }
}

def build() {
    dir(env.repo) {
        if (env.cmd) {
            sh """
                ${envExport}
                ${env.cmd}
            """
        }else {
            String buildPath = "${env.WORKSPACE}/${env.repo}"
            sh """
                ${envExport}
                chmod +x ./build.sh
                ${env.buildcmd} -d ${buildPath}/build/install
            """
        }
    }
    sh "tar czf ${packageName} ${env.repo}/build ${env.repo}/install.sh"
}

def upload() {
    def repo = env.gitlabSourceRepoName ?: env.repo
    def branch = env.gitlabSourceBranch ?: env.branch
    def commitId = env.gitlabMergeRequestLastCommit ?: env.commitId
    if (env.packagePath) {
        artifact.upload("${packageName}", env.packagePath)
    } else {
        artifact.upload(repo, branch, commitId, "${packageName}")
    }
}
runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }, handler: 'closure', async: false],
        'env-setup': [closure: { envSet() }, handler: 'closure', depends: ['checkout'], async: false],
        'build': [closure: { build() }, handler: 'closure', depends: ['env-setup'], async: false],
        'upload-package': [closure: { upload() }, handler: 'closure', depends: ['build'], async: false],
    ]
    runPipeline(workflow)
}
