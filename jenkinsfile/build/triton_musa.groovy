@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

// nodeLabel runChoice branch commitId umdPackageUrl triggerInfo compileArgs compileParallel packageName

gitLib = new git()
commonLib = new common()

env.repo = 'triton_musa'

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    packageName = env.packagePath ? env.packageName : "${env.commitId}_${env.packageName}"
}

def envSet() {
    sh 'apt update && apt install ninja-build ||:'
    sh 'rm -rf /usr/local/musa ||:'
    sh 'pip install cmake==3.25.2 ||:'
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }
    // constants.downloadAndUnzipPackage(env.condaPackageUrl, '/home/<USER>')
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    }
    else {
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
    }
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
}

def build() {
    if (env.compileTargetArch == 'x86') {
        env.exports += ";LD_LIBRARY_PATH=/usr/local/musa/lib/:/usr/lib/x86_64-linux-gnu:/usr/local/lib/musa/lib/x86_64-linux-gnu/:\$LD_LIBRARY_PATH"
    }
    else {
        env.exports += ";LD_LIBRARY_PATH=/usr/local/musa/lib:/usr/lib/aarch64-linux-gnu/musa/:/usr/lib/aarch64-linux-gnu/:\$LD_LIBRARY_PATH"
    }
    String envExport = env.exports ? 'export ' + env.exports.split(';').join(' && export ') : ''
    dir("${env.repo}/python") {
        sh """
            #! /bin/bash
            ${envExport}
            clang -v
            llvm-config --version
            python --version
            which python
            MAX_JOBS=${env.parallelJob} python setup.py bdist_wheel -d bdist_wheel/
            pip install bdist_wheel/triton-*.whl
        """
        sh "tar -czf ${packageName} ./bdist_wheel"
    }
}

def buildTest() {
    dir(env.repo) {
        String envExport = env.exports ? 'export ' + env.exports.split(';').join(' && export ') : ''
        sh """
            #!/bin/bash
            ${envExport}
            cd python/build/cmake.linux*
            ninja test
            pip install lit
            lit test
        """
    }
}

def upload() {
    dir("${env.repo}/python") {
        if (env.packagePath) {
            artifact.upload(packageName, "oss/${env.packagePath}")
        } else {
            def repo = env.gitlabSourceRepoName ?: env.repo
            def branch = env.gitlabSourceBranch ?: env.branch
            def commitId = env.gitlabMergeRequestLastCommit ?: env.commitId
            artifact.upload(repo, branch, commitId, packageName)
        }
    }
    utils.catchErrorContinue { deleteDir() }
}

runner.start(env.runChoice, [
    pre: {
        if (env.compileTargetArch == 'arm') {
            println 'reset host'
            sh 'docker run --rm --privileged sh-harbor.mthreads.com/compute/qemu-user-static --reset -p yes'
        }
    },
    main: {
        def workflow
        if (env.enableTest == 'true') {
            workflow = [
                'checkout': [closure: { fetchCode() }],
                'envSet': [closure: { envSet() }],
                'build': [closure: { build() }, maxWaitTime: [time: env.timeTolerance, unit: 'MINUTES']],
                'buildTest': [closure: { buildTest() }, maxWaitTime: [time: env.timeTolerance, unit: 'MINUTES']],
                'upload': [closure: { upload() }]
            ]
        }
        else {
            workflow = [
                'checkout': [closure: { fetchCode() }],
                'envSet': [closure: { envSet() }],
                'build': [closure: { build() }, maxWaitTime: [time: env.timeTolerance, unit: 'MINUTES']],
                'upload': [closure: { upload() }]
            ]
        }

        runPipeline(workflow)
    }
])
