@Library('swqa-ci')

import org.swqa.tools.git

/*
 * parameters
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (String) - Linux_build
 * cluster (String) - shfarm
 * podNodeSelector (String) - In=mt=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=64Gi;limits=cpu=64;limits=memory=128Gi;
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_compile:v7 //arm
 * compileTargetArch (Choice) - x86_64 [x86_64 | arm]
 * arm_musa_asm_dir (String) - ''
 * ddk_url (String) - ''
 * ddk_commit_id (String) - ''
 * makecmds_musatoolkit (String) - --build_all --build_archs=22,31 --ddk_backend=m3d -j128
 * musa_toolkit_branch (String) - release_KUAE_2.0_for_PH1_M3D
 * mtcc_branch (String) - release_KUAE_2.0_for_PH1_M3D
 * musart_branch (String) - release_KUAE_2.0_for_PH1_M3D
 * musify_branch (String) - release_KUAE_2.0_for_PH1_M3D
 * mublas_branch (String) - release_KUAE_2.0_for_PH1_M3D
 * mufft_branch (String) - release_KUAE_2.0_for_PH1_M3D
 * mupp_branch (String) - release_KUAE_2.0_for_PH1_M3D
 * murand_branch (String) - release_KUAE_2.0_for_PH1_M3D
 * mtjpeg_branch (String) - release_KUAE_2.0_for_PH1_M3D
 * musparse_branch (String) - release_KUAE_2.0_for_PH1_M3D
 * musolver_branch (String) - release_KUAE_2.0_for_PH1_M3D
 * mupti_branch (String) - release_KUAE_2.0_for_PH1_M3D
 * rename_pkg_musa_toolkits (String) - musa_toolkits_install_full.tar.gz
 * build_EnvParam (String) - ''
 * OSS_SAVE_URL (String) - oss/release-ci/computeQA/cuda_compatible/CI/${env.musa_toolkit_branch}/${BUILD_TIMESTAMP}/
*/

gitLib = new git()

musaToolkitRepo = 'musa_toolkit'
toolkit_version = ''
toolkit_commit_id = ''
member_commit_id = [:]
target_toolkit_name = 'musa_toolkits_install_full.tar.gz'

gnu_dir = env.compileTargetArch == 'x86_64' ? 'x86_64-linux-gnu' : 'aarch64-linux-gnu'

def setupMtPerf() {
    latest_mt_perf = utils.curl('http://oss.mthreads.com/release-ci/mt-perf/develop/latest.txt')
    if (env.compileTargetArch == 'x86_64') {
        if (env.musa_toolkit_branch == 'master') {
            latest_mt_perf += '_linux_x86_64.tar.gz'
        }
        else {
            latest_mt_perf += '_linux_x86_64_kuae.tar.gz'
        }
    } else {
        latest_mt_perf += '_linux_aarch64.tar.gz'
    }

    print(latest_mt_perf)
    sh "wget ${latest_mt_perf} --no-check-certificate"
    sh 'tar -xvf ./*mt-perf*.tar.gz'
    sh 'mkdir /usr/local/mt-perf'
    sh 'cp -r ./*mt-perf*/* /usr/local/mt-perf'
}

def fetchCode() {
    toolkit_members = [ [name: 'mtcc', branch: env.mtcc_branch] ]
    if (env.musart_branch && env.musa_toolkit_branch in ['master']) {
        toolkit_members.add([name: 'MUSA-Runtime', branch: env.musart_branch])
    }
    if (env.musify_branch) { toolkit_members.add([name: 'musify', branch: env.musify_branch]) }
    if (env.mublas_branch) { toolkit_members.add([name: 'muBLAS', branch: env.mublas_branch]) }
    // muBLASLt repo has only been added to master so far.
    if (!env.musa_toolkit_branch.contains('KUAE')) {
        if (env.mublaslt_branch) { toolkit_members.add([name: 'muBLASLt', branch: env.mublaslt_branch]) }
    }
    if (env.mufft_branch) { toolkit_members.add([name: 'muFFT', branch: env.mufft_branch]) }
    if (env.mupp_branch) { toolkit_members.add([name: 'muPP', branch: env.mupp_branch]) }
    if (env.murand_branch) { toolkit_members.add([name: 'muRAND', branch: env.murand_branch]) }
    if (env.musparse_branch) { toolkit_members.add([name: 'muSPARSE', branch: env.musparse_branch]) }
    if (env.mtjpeg_branch && env.makecmds_musatoolkit.contains('build_mtjpeg')) { toolkit_members.add([name: 'MTJPEGSDK', branch: env.mtjpeg_branch]) }
    if (env.musolver_branch) { toolkit_members.add([name: 'muSOLVER', branch: env.musolver_branch]) }
    if (env.mupti_branch && env.makecmds_musatoolkit.contains('build_mupti')) { toolkit_members.add([name: 'MUPTI', branch: env.mupti_branch]) }

    toolkit_commit_id = gitLib.fetchCode(musaToolkitRepo, env.musa_toolkit_branch, null, [disableSubmodules: true])
    dir(musaToolkitRepo) {
        gitLib.fetchCode('module_version', 'master')
        toolkit_members.each { entry ->
            commit = gitLib.fetchCode(entry.name, entry.branch)
            member_commit_id[entry.name] = commit
        }
    }
}

def setupDepends() {
    sh 'rm /bin/sh && ln -s /bin/bash /bin/sh'
    sh 'apt update -y ||: && apt install pigz -y ||:'
    oss.install()

    if (env.makecmds_musatoolkit.contains('-DMT_PERF_PATH')) {
        setupMtPerf()
    }

    if (env.compileTargetArch == 'x86_64') {
        oss.cp('oss/release-ci/computeQA/tools/cmake-3.25.1-linux-x86_64.tar.gz')
        sh 'tar xf cmake-3.25.1-linux-x86_64.tar.gz && rm -rf /usr/bin/cmake && ln -s `pwd`/cmake-3.25.1-linux-x86_64/bin/cmake /usr/bin/'

        sh """
            wget --no-check-certificate ${env.musaasmPackageUrl} -O musa_asm.tar.gz
        """
    }
    else {
        // oss.cp('oss/release-ci/computeQA/ai-rely-pkg/conda/aarch64/mathx.tar')
        // sh 'tar xf mathx.tar && mkdir -p /home/<USER>/home/<USER>/anaconda3 && ln -s `pwd`/anaconda3 /home/<USER>'

        sh """
            wget --no-check-certificate ${env.musaasmPackageUrl} -O musa_asm.tar.gz
        """
    }

    sh '''
        tar -xzvf musa_asm.tar.gz
        cd musa_asm/build/bin
        mkdir -p /usr/local/musa/bin
        cp musaasm /usr/local/musa/bin/
        cp musaasm /usr/local/bin/
        pwd
        ls -l
    '''

    dir(musaToolkitRepo) {
        toolkit_version = sh(script: "grep '^version=' build.sh |sed -n 's/^version=\\(.*\\)/\\1/p'", returnStdout: true).trim()
    }
}

def setupDDK() {
    def isDeb = sh(script: 'command -v dpkg-deb >/dev/null 2>&1', returnStatus: true) == 0
    def ddk_url = constants.ossPathToUrl(env.ddk_url)
    constants.downloadPackage(ddk_url)
    def ddk_file = env.ddk_url.split('/')[-1]
    if (isDeb) {
        sh """
            pwd
            ls -l
            dpkg-deb -x ${ddk_file} ddk_2_dir
            dpkg-deb -e ${ddk_file} ddk_2_dir/DEBIAN
        """
    } else {
        sh '''
            pwd
            ls -l
            mkdir ddk_2_dir && cd ddk_2_dir
            rpm2cpio ../*_ddk2.0.*.rpm | cpio -idmv
        '''
    }
    if (env.musa_toolkit_branch.contains('M1000')) {
        sh """
            wget --no-check-certificate ${env.musart_url} -O musa_runtime.tar.gz
            mkdir musa_runtime_dir
            tar -xzf musa_runtime.tar.gz -C ./musa_runtime_dir

            mkdir -p ${env.WORKSPACE}/${musaToolkitRepo}/build/musa_toolkits_${toolkit_version}
            cp -r musa_runtime_dir/usr/local/musa/* ${env.WORKSPACE}/${musaToolkitRepo}/build/musa_toolkits_${toolkit_version}/
        """
    }
    else {
        sh """
            cp -a ddk_2_dir/usr/lib/${gnu_dir}/libdrm_mtgpu.so* ddk_2_dir/usr/local/musa/lib/ || :
            mkdir -p ${env.WORKSPACE}/${musaToolkitRepo}/build/musa_toolkits_${toolkit_version}
            cp -r ddk_2_dir/usr/local/musa/* ${env.WORKSPACE}/${musaToolkitRepo}/build/musa_toolkits_${toolkit_version}/
        """
    }
}

def build() {
    dir(musaToolkitRepo) {
        credentials.runWithCredential('SSH_GITLAB') {
            sh """
                ${env.build_EnvParam}
                export PATH=/usr/local/musa/bin:/usr/local/bin:\${PATH}
                export LD_LIBRARY_PATH=/usr/lib/${gnu_dir}/musa/:/usr/lib/${gnu_dir}/:/usr/local/musa/lib:\${LD_LIBRARY_PATH}
                cd ${env.WORKSPACE}/musa_asm/build/bin
                mkdir -p ${env.WORKSPACE}/${musaToolkitRepo}/build/musa_toolkits_${toolkit_version}/bin/
                cp musaasm ${env.WORKSPACE}/${musaToolkitRepo}/build/musa_toolkits_${toolkit_version}/bin/
                cd ${env.WORKSPACE}/${musaToolkitRepo} && ./build.sh ${env.makecmds_musatoolkit} 2>&1 | tee log_build_musatoolkits.txt
            """
        }

        buildFile = "build/${env.BUILD_TIMESTAMP}_${musaToolkitRepo}.txt"
        sh """
            touch ${buildFile}
            echo "project:${musaToolkitRepo}" >> ${buildFile}
            echo "branch:${env.musa_toolkit_branch}" >> ${buildFile}
            echo "commitID:${toolkit_commit_id}" >> ${buildFile}
            echo "ddk_commitID:${env.ddk_commit_id}" >> ${buildFile}
        """
        member_commit_id.each { entry ->
            def version_output = sh(script: "./build/musa_toolkits_${toolkit_version}/bin/musa_version_query", returnStdout: true).trim().toLowerCase()
            def pattern
            if (entry.key.toLowerCase() == 'mtcc') {
                pattern = ~/m(t?)cc/
            } else {
                def normalizedPattern = entry.key.toLowerCase().replace('-', '_')
                pattern = ~/${normalizedPattern}/
            }
            def compile_success = (version_output =~ pattern).find()
            if (!compile_success) {
                error "Build failed for ${entry.key}"
            }

            sh """
                echo "${entry.key}_commitId:${entry.value}" >> ${buildFile}
            """
        }

        sh "rm -rf build/musa_toolkits_${toolkit_version}/bin/musaasm"
        if (env.compileTargetArch != 'x86_64') {
            target_toolkit_name = 'musa_toolkits_install_full.tar.gz'
        }

        sh "cd build && tar -Ipigz -cf ../${target_toolkit_name} --exclude='musa_toolkits_version_config' musa_toolkits_* ????-??-??*.txt"
        sh 'ls -al'
    }
}

def upload() {
    dir(musaToolkitRepo) {
        def pkg_name = target_toolkit_name
        if (env.rename_pkg_musa_toolkits) {
            sh "mv ${pkg_name} ${env.rename_pkg_musa_toolkits}"
            pkg_name = env.rename_pkg_musa_toolkits
        } else {
            sh "mv ${pkg_name} ${env.ddk_commit_id}_${pkg_name}"
            pkg_name = "${env.ddk_commit_id}_${pkg_name}"
        }
        artifact.upload("${pkg_name}", env.OSS_SAVE_URL)
    }
}

runner.start(env.runChoice, [
    pre: {
        if (env.compileTargetArch == 'arm') {
            println 'reset host'
            sh 'docker run --rm --privileged sh-harbor.mthreads.com/compute/qemu-user-static --reset -p yes'
        }
    },
    main: {
        def workflow = [
            'checkout': [closure: { fetchCode() }],
            'setupDepends': [closure: { setupDepends() }],
            'setupDDK': [closure: { setupDDK() }],
            'build': [closure: { build() }],
            'upload': [closure: { upload() }]
        ]
        runPipeline(workflow)
    }
])
