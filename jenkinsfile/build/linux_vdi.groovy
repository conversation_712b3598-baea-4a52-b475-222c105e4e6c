@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

/*
 * parameters
 * repo (String) - gr-kmd
 * branch (String) - vgpu_2.6.5_release
 * commitId (String) - null
 * kernelverDependency (String) - https://oss.mthreads.com/release-ci/ci_tool/Linux_CI/arm64_kernel_header/kylin_4.19.90-52.22.v2207.ky10.aarch64.tar.gz
 * umdDependency (String) - https://oss.mthreads.com/release-ci/VDI/XC-VDI/${branch}/repoPackages/guest_gr-umd/${latestUmdCommit}_x86_64-mtgpu_linux-xorg-release-umd.tar.gz
 * mediaDependency (String) - https://oss.mthreads.com/release-ci/VDI/XC-VDI/${branch}/repoPackages/guest_mt-media-driver/${latestMediaCommit}_video_release_vgpu.tar.gz
 * exports (Multiline String):
    {
        "VGPU_FW_1_0" : "1",
        "SUPPORT_LOAD_WINDOWS_FIRMWARE" : "0",
        "VGPU_COMPAT_CHECK_MODE_VERSION_LIST" : "1"
    }
 * cmd (String) - ./kmd_build.sh -b release -w xorg -p mtgpu_linux -d 0 -r 1.0.0.0 -o hw -g deb -v 1 -x 2.6.5-000 -j8
 * targetName (String) - guest_gr-kmd
 * binPath (String) - gr-kmd/binary_x86_64_mtgpu_linux_xorg_1.0.0.0_hw_release/target_x86_64/
 * packageName (String) - mtgpu-2.6.5.amd64.deb
 * ossPkgPath (String) - release-ci/VDI/XC-VDI/vgpu_2.6.5_release/repoPackages/guest_gr-kmd/
 * updateLatest (Choice) - true
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd:v53
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=8;requests=memory=8Gi;limits=cpu=8;limits=memory=16Gi
 * triggerInfo
*/

gitLib = new git()
commonLib = new common()
ccachePath = '/home/<USER>/umd_ccache'
// env.mountParms = env.repo == 'gr-umd' ? "-v ${ccachePath}:${ccachePath}" : ''

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [submoduleShallow: false, updateBuildDescription: true])
    sh """
        cp -rf ${env.repo} /root/
        ls -l /root ||:
    """
    if (env.kernelverDependency) {
        dir('kernel_header') {
            constants.downloadAndUnzipPackage(env.kernelverDependency)
        }
        sh 'cp -rf kernel_header /root/kernel_header'
    }
    if (env.umdDependency) {
        dir('gr_umd_dist') {
            constants.downloadAndUnzipPackage(env.umdDependency)
        }
        sh 'cp -rf gr_umd_dist /root/gr_umd_dist'
    }
    if (env.mediaDependency) {
        constants.downloadAndUnzipPackage(env.mediaDependency)
        sh '''
            mkdir -p /root/gr-kmd/ci_win_fw
            cp -rf /root/gr_umd_dist/*mtgpu_linux*/lib/firmware/mthreads/musa* /root/gr-kmd/ci_win_fw
            cp -rf mt_video/firmware/mtvpu*.bin /root/gr-kmd/ci_win_fw
            ls -l /root/gr-kmd/ci_win_fw ||:
        '''
    }
}

def build() {
    String envExport = env.exports ? commonLib.genExportsFromJson(env.exports).join(';') : ''
    // def ccachedCmd = env.repo == 'gr-umd' ? commonLib.ccachedCmd(ccachePath) : ''
    String distPath = "/root/${env.targetName}/dist"
    credentials.runWithCredential('SSH_GITLAB') {
        sh """
            cd /root/${env.repo}
            ${envExport}
            if [ "${env.repo}" = "gr-umd" ]; then
                UMD_BRANCH_VERSION=${env.gitlabTargetBranch ?: env.branch}
            fi
            if [ "${env.repo}" = "mt-media-driver" ]; then
                ${env.cmd}
            else
                ${env.cmd} -i ${distPath}
                ${env.cmd} -i ${distPath} install
            fi
        """
    }
}

def upload() {
    env.ossPkgPath = env.ossPkgPath ?: "release-ci/VDI/XC-VDI/${env.branch}/repoPackages/${env.targetName}/"
    String fullPkgName = "${env.commitId}_${env.packageName}"
    String distPath = env.binPath ? "/root/${env.binPath}" : "/root/${env.targetName}/dist"
    if (distPath.contains('video')) {
        sh "cd ${distPath}; mv mt_video_dist mt_video; tar cvzf ${env.packageName} mt_video/"
    }
    if (distPath.contains('gr-umd')) {
        String folderName = sh(script: "ls ${distPath}", returnStdout: true).trim()
        utils.uploadDbgSym("${distPath}/${folderName}", 'oss/debug-symbols/scripts/upload_umd_dbgsym')
        sh """
            cd ${distPath}
            tar cvzf ${env.packageName} ${folderName}
        """
    }
    sh """
        cd ${distPath}
        mv ${env.packageName} ${fullPkgName}
    """
    artifact.upload("${distPath}/${fullPkgName}", env.ossPkgPath)
    if (env.updateLatest.toBoolean()) {
        sh """
            echo https://oss.mthreads.com/${env.ossPkgPath + env.commitId}_ > latest.txt
            mc cp latest.txt oss/${env.ossPkgPath}
        """
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }],
        'upload': [closure: { upload() }],
    ]

    runPipeline(workflow)
}
