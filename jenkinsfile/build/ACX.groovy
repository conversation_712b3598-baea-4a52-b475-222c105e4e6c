@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * linuxDdkBranch (String) - master
 * linuxDdkCommitId (String) - ''
 * aospBranch (String) - main
 * aospCommitId (String) - ''
 * packageName (String) - ''
 * exports (Multiline String) default '', split by '\n'
 * asopCmd (String) - 'lunch armsrv-userdebug && make -j90'
 * linuxDdkCmd (String) - './ddk_build.sh -a 0 -u 3'
 * packagePath(String) - ''
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - Linux_build
 * containerImage (String) - sh-harbor.mthreads.com/build-env/aosp:v5
*/

// env.buildAsop  env.buildAndroidSdk
env.repo = 'linux-ddk'

def fetchCode() {
    gitLib.installGitLfs()
    env.mountParms = "-v ${env.WORKSPACE}:/root/mt"
    env.linuxDdkCommitId = gitLib.fetchCode('linux-ddk', env.linuxDdkBranch, env.linuxDdkCommitId)
    if (env.buildAsop?.toString() == 'true') {
        credentials.runWithCredential('SSH_GITLAB') {
            sh "git clone -b ${env.aospBranch} ************************:sw/android_arm64.git && cd android_arm64 && git submodule update --init --recursive && cd .."
        }
    } else {
        // 检查 /root/mt/android_arm64 是否存在
        def androidDirExists = sh(script: 'test -d /root/mt/android_arm64 && echo 1 || echo 0', returnStdout: true).trim() == '1'
        if (androidDirExists) {
            // 复制到 env.WORKSPACE 目录
            sh "cp -r /root/mt/android_arm64 ${env.WORKSPACE}/"
        }
    }
    // 新增：判断 buildAndroidSdk
    if (env.buildAndroidSdk?.toString() != 'true') {
        def sdkDirExists = sh(script: 'test -d /root/mt/android-sdk && echo 1 || echo 0', returnStdout: true).trim() == '1'
        if (sdkDirExists) {
            sh "cp -r /root/mt/android-sdk ${env.WORKSPACE}/"
        }
    }
}

def buildAsop() {
    sh '''
        cd /root/mt/android_arm64
        ls -l /bin/sh
        export ANDROID_ROOT=/root/mt/android_arm64
        cd prebuilts/
        rm -rf jdk/
        wget -q --no-check-certificate https://oss-swci-sh.mthreads.com/dependency/SWQA-CI/Linux/jdk.tar && tar -xvf jdk.tar && cd ../
    '''
    sh """
        cd /root/mt/android_arm64
        ls -l /bin/sh
        export ANDROID_ROOT=/root/mt/android_arm64
        . build/envsetup.sh
        ${env.asopCmd}
    """
}

def buildLinuxDdk() {
    // 新增：判断 workspace 里是否有 android-sdk，有则复制到 /root/
    def wsSdkExists = sh(script: 'test -d /root/mt/android-sdk && echo 1 || echo 0', returnStdout: true).trim() == '1'
    if (wsSdkExists) {
        sh 'mv /root/mt/android-sdk /root/'
    }
    sh '''
        cd /root/mt/linux-ddk
        ls -l /bin/sh
        cd gr-umd
        build/linux/tools/prepare-all-android.sh -a build -t "armeabi-v7a arm64-v8a x86 x86_64" -j90
    '''
    sh """
        cd /root/mt/linux-ddk
        ls -l /bin/sh
        ${env.linuxDdkCmd}
    """
    // 新增：编译后把 android-sdk 复制到 /root/mt
    if (wsSdkExists) {
        sh 'cp -rf /root/android-sdk /root/mt/'
    }
}

def upload() {
    // 确保 /root/mt 目录存在
    sh 'mkdir -p /root/mt'
    // 编译产物后，复制 android-sdk 和 android_arm64 到 /root/mt，允许失败
    sh "cp -rf ${env.WORKSPACE}/android_arm64 /root/mt/ || true"
    sh "cp -rf ${env.WORKSPACE}/android-sdk /root/mt/ || true"
    dir("${env.WORKSPACE}/android_arm64/out/target/product/armsrv/") {
        sh """
            tar czf ${env.packageName} vendor/
        """
        def repo = env.gitlabSourceRepoName ?: env.repo
        def branch = env.gitlabSourceBranch ?: env.linuxDdkBranch
        def commitId = env.gitlabMergeRequestLastCommit ?: env.linuxDdkCommitId
        if (env.packagePath) {
            artifact.upload("${packageName}", env.packagePath)
        } else {
            artifact.upload(repo, branch, commitId, "${packageName}")
        }
    }
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'buildAsop': [closure: { buildAsop() }],
        'buildLinuxDdk': [closure: { buildLinuxDdk() }],
    ], [disablePre: true, disablePost: true])
}, post: {
    runPipeline([
        'upload': [closure: { upload() }]
    ], [disablePre: true])
}, pre: {
    runPipeline([
        'setup pre': [closure: { fetchCode() }],
    ], [disablePost: true])
}])
