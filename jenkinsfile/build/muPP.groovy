@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
 * packageName (String) - muPP.tar.gz
 * linuxDdkPackageUrl (String) - ''
 * mtccPackageUrl (String) - 'https://oss.mthreads.com/release-ci/computeQA/musa/newest/mtcc-nightly-x86_64-linux-gnu-ubuntu-20.04.tar.gz'
 * anacondaPackageUrl(String) - 'https://oss.mthreads.com/release-ci/computeQA/tools/musify.tar;oss/release-ci/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
 * gcov(String) - [ON | OFF]
 * exports (Multiline String) default '', split by '\n'
 * compileArgs (String) -DMUSA_ARCHS=21 -DOPENCV_INC:PATH=/usr/local/include/opencv4 -DOPENCV_LIB:PATH=/usr/local/lib -DIPP_INC=/home/<USER>/ipp/latest/include -DIPP_LIB=/home/<USER>/ipp/latest/lib/intel64 -DINSTALL_DIR=`pwd`/install
 * compileParallel (String) -16
 * packagePath(String) - ''
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/qa/mupp_compile-ubuntu-22-04:v1
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=2;requests=memory=5Gi;limits=cpu=10;limits=memory=25Gi
*/

env.repo = 'muPP'

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    packageName = env.packagePath ? env.packageName : "${env.gitlabMergeRequestLastCommit ? constants.formatCommitID(env.gitlabMergeRequestLastCommit) : env.commitId}_${env.packageName}"
}

def envSetUp() {
    ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    def dependencies = ['mtcc': env.mtccPackageUrl]
    installDependency(dependencies)
    constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
    if (env.musifyPackageUrl) {
        musa.installMusify(env.musifyPackageUrl)
    }
}

def build() {
    String envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    envExport = env.gcov == 'ON' ? env.exports + '&& export GCOV_TEST=ON' : envExport
    dir(env.repo) {
        if (env.cmd) {
            sh """
                ${envExport}
                ${env.cmd}
            """
        }else {
            sh """
                ${envExport}
                mkdir build && cd build
                ${constants.genCondaActivate('mathx')}
                cmake .. ${compileArgs} && make -j${compileParallel}
                cd .. && cmake --install build ||:
            """
        }
    }
    sh "tar czf ${packageName} ${env.repo}/build ${env.repo}/install.sh"
}

def upload() {
    def repo = env.gitlabSourceRepoName ?: env.repo
    def branch = env.gitlabSourceBranch ?: env.branch
    def commitId = env.gitlabMergeRequestLastCommit ?: env.commitId
    if (env.packagePath) {
        artifact.upload("${packageName}", env.packagePath)
    } else {
        artifact.upload(repo, branch, commitId, "${packageName}")
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'fetchCode': [closure: { fetchCode() }],
        'envSetUp': [closure: { envSetUp() }],
        'build': [closure: { build() }],
    ]
    if (env.packageName) { workflow['upload'] = [closure: { upload() }] }

    runPipeline(workflow)
}
