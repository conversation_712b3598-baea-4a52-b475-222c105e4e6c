@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

def time_tag_name = new Date().format('MM/dd/yyyy')
version = "${time_tag_name}"

def initEnv() {
    if (env.NODE_NAME?.toLowerCase()?.contains('server')) {
        echo "NODE_NAME contains 'server', skip initEnv."
        return
    }
    new common().initEnv(env.NODE_NAME, "192.168.${env.NODE_NAME.split('_')[-1]}")
}

def fetchAndUpdateRepo(String repoName, String branch, String disableSubmodules = '', int depth = 1) {
    new git().fetchCode(repoName, branch, null, [preBuildMerge: false, disableSubmodules: true, shallow: true, updateBuildDescription: true])
    new git().updateSubmodule(repoName, depth, '', disableSubmodules)
}

def moveAndClean(String src, String dest, int depth = 1) {
    if (src == 'm3d') {
        env.commitId = new git().fetchCode(env.repo, env.branch, env.commitId, [disableSubmodules: true, updateBuildDescription: true])
        new git().updateSubmodule(env.repo, depth)
    }
    dir(dest) {
        deleteDir()
    }
    bat "move /Y ${src} ${dest}"
}

def fetchM3dCode() {
    def repos = [
        [name: 'wddm', branch: 'develop', disableSubmodules: 'dxc,ogl,mtdxum,Vulkan,import/mtcc'],
        [name: 'ogl', branch: 'master', disableSubmodules: 'imported\\m3d'],
        [name: 'dxc', branch: 'master', disableSubmodules: 'imported\\m3d'],
        [name: 'mtdxum', branch: 'master', disableSubmodules: 'imported\\m3d'],
        [name: 'Vulkan', branch: 'master', disableSubmodules: 'imported\\m3d'],
        [name: 'mtkmd', branch: 'master', disableSubmodules: 'm3d']
    ]
    repos.each { repo ->
        fetchAndUpdateRepo(repo.name, repo.branch, repo.disableSubmodules)
    }

    [
        ['ogl', 'wddm\\ogl'],
        ['dxc', 'wddm\\dxc'],
        ['mtdxum', 'wddm\\mtdxum'],
        ['Vulkan', 'wddm\\vulkan'],
        ['mtkmd', 'wddm\\kmd'],
        ['m3d', 'wddm\\ogl\\imported\\m3d'],
        ['m3d', 'wddm\\dxc\\imported\\m3d'],
        ['m3d', 'wddm\\mtdxum\\imported\\m3d'],
        ['m3d', 'wddm\\vulkan\\imported\\m3d'],
        ['m3d', 'wddm\\kmd\\m3d'],
        ['m3d', 'wddm\\kmd\\exmod\\mt-rm\\common\\m3d']
    ].each { pair ->
        moveAndClean(pair[0], pair[1])
    }
}

def fetchCode() {
    def dependencyConfigs = env.dependencyRepos ? readJSON(text: env.dependencyRepos) : null

    env.commitId = new git().fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true, disableSubmodules: true])
    if (env.repo?.toLowerCase() == 'mt-vgpu') {
        new git().updateSubmodule(env.repo, 1, '', 'linux-ddk')
    } else {
        new git().updateSubmodule(env.repo)
    }

    if (dependencyConfigs) {
        dependencyConfigs.each { dependencyConfig ->
            def dependencyRepoCommitid = dependencyConfig.name == 'wddm' ? env.wddmCommitId : null

            new git().fetchCode(dependencyConfig.name, dependencyConfig.branch, dependencyRepoCommitid, [preBuildMerge: false, disableSubmodules: true, shallow: false, updateBuildDescription: true])
            new git().updateSubmodule(dependencyConfig.name, 1, dependencyConfig.submodules, dependencyConfig.skip_submodules)
        }
    }

    if (dependencyConfigs) {
        dependencyConfigs.each { dependencyConfig ->
            if (dependencyConfig.links) {
                dependencyConfig.links.each { l ->
                    dir(l.link) {
                        deleteDir()
                    }
                    bat "mklink /J ${l.link} ${l.target}"
                }
            }
        }
    }
}

def fetchDepdepPackage() {
    def artifacts = env.externalArtifacts ? readJSON(text: env.externalArtifacts) : []
    def ossBranchPath = constants.genOssPath(env.repo, env.branch, env.commitId)
    def depdepCommitId = ossBranchPath.tokenize('/')[-1]
    def prefix = constants.genOssPrefix(ossBranchPath)
    def depPackageUrl = env.depPackageUrl ?: "${prefix}/${ossBranchPath}/${depdepCommitId}"
    new common().retryByRandomTime({
        artifacts.each { artifact ->
            def fileName = artifact.name
            def extractPath = artifact.extractPath
            def customDepPackageUrl = artifact.depPackageUrl ?: depPackageUrl
            def realPkgUrl = null

            if (customDepPackageUrl.endsWith('.txt')) {
                def txtContent = sh(script: "curl --insecure ${customDepPackageUrl}", returnStdout: true).trim()
                if (txtContent ==~ /.*\.(tar\.gz|zip|tgz)$/) {
                    realPkgUrl = txtContent
                } else if (fileName.toLowerCase().contains('wddm')) {
                    def baseName = txtContent.tokenize('/').last()
                    realPkgUrl = "${txtContent}/${baseName}_${fileName}"
                } else {
                    realPkgUrl = "${txtContent}_${fileName}"
                }
            } else if (customDepPackageUrl ==~ /.*\.(tar\.gz|zip|tgz)$/) {
                realPkgUrl = customDepPackageUrl
            } else {
                realPkgUrl = "${customDepPackageUrl}_${fileName}"
            }

            dir(extractPath) {
                bat """wget -q "${realPkgUrl}" -O "${fileName}" --no-check-certificate"""
                if (env.repo == 'mtcc') {
                    bat """
                        del /F /Q .\\Win\\musa_compiler_shared.tar.gz
                        tar xvzf "${fileName}" -C .
                        tar xvzf .\\Win\\musa_compiler_shared.tar.gz -C .\\Win\\
                    """
                } else {
                    bat """
                        tar xvzf "${fileName}" -C .
                        del "${fileName}"
                    """
                }
            }
        }
    }, 20)
}

def fetchWDDMDriver() {
    if (!env.wddmDriverUrl || env.updateDriver == 'false') {
        return
    }

    def wddmPkgName = "wddm_${env.driverType}.tar.gz"
    def downloadDriverUrl = "${env.wddmDriverUrl}_${wddmPkgName}"
    def pkgDir = env.packageDir ?: env.outputDir
    new common().retryByRandomTime({
        dir(pkgDir) {
            bat """
                wget -q "${downloadDriverUrl}" -O "${wddmPkgName}" --no-check-certificate
                tar -xvzf "${wddmPkgName}" -C .
                del "${wddmPkgName}"
            """
        }
    }, 20)
}

def copyVulkanExtraFiles(pkgDir) {
    bat "xcopy /E /I /Y \"wddm\\vulkan\\ci\\win\\cts_ci_win.txt\" \"${pkgDir}\\\" || exit 0"
}

def packageProducts(distPath, pkgDir, products) {
    def distPaths = distPath.split(',')
    if (products) {
        def notCopied = []
        products.each { prod ->
            def lowerProd = prod.toLowerCase()
            def targetDir
            if (prod == 'pdump.exe') {
                targetDir = pkgDir
            } else if (lowerProd.endsWith('.pdb') || lowerProd.endsWith('.exe')) {
                targetDir = "${pkgDir}\\symbols"
            } else {
                targetDir = pkgDir
            }
            if (!fileExists(targetDir)) {
                bat "mkdir \"${targetDir}\""
            }
            def targetFile = "${targetDir}\\${prod}"

            def prodDistPath = null
            if (prod.contains('mtvk64.json')) {
                prodDistPath = 'wddm\\Vulkan\\ci\\win'
            } else if (prod.contains('mticdfbg') || prod.contains('mticdpxg')) {
                prodDistPath = 'wddm\\ogl\\imported\\pre-binary-kmd\\hw'
            } else {
                distPaths.each { p ->
                    def prodPath = "${p}\\${prod}"
                    echo "Check fileExists: ${prodPath}"
                    if (fileExists(prodPath)) {
                        prodDistPath = p
                    }
                }
            }
            if (!prodDistPath) {
                echo "Warning: ${prod} not found in any distPath: ${distPaths}"
                notCopied << prod
                return
            }

            if (fileExists(targetFile)) {
                bat "del /F /Q \"${targetFile}\""
            }
            def copyCmd = "copy /Y \"${prodDistPath}\\${prod}\" \"${targetFile}\""
            def copyResult = bat(script: "${copyCmd}", returnStatus: true)
            if (copyResult != 0) {
                notCopied << prod
                error "Failed to copy ${prod} from ${prodDistPath} to ${targetFile}"
            }
        }
        if (!notCopied.isEmpty()) {
            error "The following products were not copied: ${notCopied.join(', ')}"
        }
    } else {
        distPaths.each { p ->
            if (fileExists(p)) {
                bat "xcopy /Y /E /I \"${p}\\*\" \"${pkgDir}\\\""
            } else {
                error "Warning: distPath ${p} does not exist, skip xcopy."
            }
        }
    }

    if (env.gitlabActionType == 'MERGE' || env.gitlabActionType == 'NOTE') {
        switch (env.repo?.toLowerCase()) {
            case 'wddm':
            case 'm3d':
                copyVulkanExtraFiles(pkgDir)
                break
        }
    }
}

def build() {
    echo "[build] start build, env.repo=${env.repo}, env.branch=${env.branch}, env.commitId=${env.commitId}, env.driverType=${env.driverType}, env.gitlabActionType=${env.gitlabActionType}"
    def steps = readJSON(text: env.steps)
    def timelimit = env.timelimit ? (env.timelimit as Integer) : 120

    timeout(time: timelimit, unit: 'MINUTES') {
        steps.each { step ->
            echo "[build] step: ${step}"
            if (step.ufPath && step.ufcPath) {
                echo "[build] refreshUF: ufPath=${step.ufPath}, ufcPath=${step.ufcPath}"
                refreshUF(step.ufPath, step.ufcPath)
            }

            echo "[build] buildPath: ${step.buildPath}, cmd: ${step.cmd}"
            dir(step.buildPath) {
                bat step.cmd
            }
            def distPath = step.distPath ?: ''
            def pkgDir = env.packageDir ?: env.outputDir
            echo "[build] distPath: ${distPath}, pkgDir: ${pkgDir}, products: ${step.products}"
            if (distPath && pkgDir) {
                packageProducts(distPath, pkgDir, step.products)
            }
        }
    }

    publishHTMLReport()
    echo '[build] build finished'
}

def publishHTMLReport() {
    if (env.publishHTML) {
        try {
            def publishParams = readJSON(text: env.publishHTML)
            publishHTML(target: [
                allowMissing: publishParams.allowMissing ?: false,
                alwaysLinkToLastBuild: publishParams.alwaysLinkToLastBuild ?: false,
                keepAll: publishParams.keepAll ?: false,
                reportDir: publishParams.reportDir ?: '',
                reportFiles: publishParams.reportFiles ?: '',
                reportName: publishParams.reportName ?: ''
            ])
        } catch (MissingMethodException e) {
            println '[publishHTMLReport] publishHTML plugin is not configured on Jenkins. Skipping HTML report publishing.'
        } catch (e) {
            error "[publishHTMLReport] An error occurred while publishing HTML report: ${e.message}"
        }
    }
}

def replaceInfIfExists(pkg) {
    def infFullPath = "${WORKSPACE}\\${pkg.infPath}\\${pkg.infFiles}"
    def pkgDir = env.packageDir ?: env.outputDir
    if (pkg.infFiles && pkg.infPath && fileExists(infFullPath)) {
        bat "del /F /Q \"${pkgDir}\\*.inf\""
        bat "copy /Y \"${infFullPath}\" \"${pkgDir}\\\""
    }
}

def upload() {
    if (env.needUpload == 'false') {
        return
    }

    if (env.packages) {
        def packages = env.packages instanceof String ? readJSON(text: env.packages) : env.packages
        packages.each { pkg ->
            if (pkg.packageName?.endsWith('_vdi.tar.gz')) {
                winBuild.fetchMtapi(pkg.packageDir)
            }
            replaceInfIfExists(pkg)
            echo "pkg.needSign: ${pkg.needSign}"
            if (pkg.needSign) {
                echo "Signing package: ${pkg.packageName} with inf: ${pkg.infFiles} at path: ${pkg.infPath}"
                signature(version, pkg.packageDir, pkg.infPath, pkg.infFiles)
            }
            def pkgName = "${env.commitId}_${pkg.packageName}"

            if (pkgName.endsWith('.zip')) {
                dir(pkg.packageDir) {
                    bat "\"C:\\Program Files\\7-Zip\\7z.exe\" a -tzip ..\\${pkgName} *"
                }
            } else if (pkgName.endsWith('.tar.gz')) {
                bat "tar -cvzf ${pkgName} -C ${pkg.packageDir} *"
            } else {
                bat "tar -cvzf ${pkgName} -C ${pkg.packageDir} *"
            }

            artifact.upload(env.repo, env.branch, env.commitId, "${pkgName}")
        }
    } else {
        if (env.needSign == 'true') {
            signature(version, env.packageDir, env.infPath, env.infFiles)
        }
        def pkgName = "${env.commitId}_${env.packageName}"

        if (pkgName.endsWith('.zip')) {
            dir(env.packageDir) {
                bat "\"C:\\Program Files\\7-Zip\\7z.exe\" a -tzip ..\\${pkgName} *"
            }
        } else if (pkgName.endsWith('.tar.gz')) {
            bat "tar -cvzf ${pkgName} -C ${env.packageDir} *"
        } else {
            bat "tar -cvzf ${pkgName} -C ${env.packageDir} *"
        }

        artifact.upload(env.repo, env.branch, env.commitId, "${pkgName}")
    }
}

def refreshUF(String ufPath, String ufcPath) {
    def ufConfig = readJSON text: env.excludeConfig
    ufConfig.each {
        def ufPath_m3d = "${ufPath}/${it.path}"
        def excludeList = it.exclude

        dir(ufPath_m3d.replace('/', '\\')) {
            bat """
                xcopy /y ${env.WORKSPACE}\\${ufcPath}offlinePipelineCompile.py .
                python offlinePipelineCompile.py --ufc ${env.WORKSPACE}\\${ufcPath} --exclude ${excludeList}
            """
        }
    }
}

def upload_directstreamTest(String directstreamPath = '') {
    def defaultDriverPath = 'DirectStream'
    directstreamPath = directstreamPath ?: defaultDriverPath
    def package_name = "${env.commitId}_directstreamTest.tar.gz"
    dir(directstreamPath) {
        bat """
            md test\\x64
            copy /Y build\\MTEncodeBuild\\x64\\Release\\*.exe test\\x64
            copy /Y build\\MTEncodeBuild\\Release\\*.exe test\\
            tar -cvzf ${package_name}  test
        """

        artifact.upload(env.repo, env.branch, env.commitId, "${package_name}")
    }
}

def upload_m3dTest(String m3dPath = 'wddm\\mtdxum\\imported\\m3d', String sharedIncludePath = "${env.WORKSPACE}/wddm/shared_include") {
    def testDir = "${m3dPath}\\test"
    def buildDir = "${testDir}\\build"
    def cmakeOptions = '-DM3D_UNIFIED_FW=ON'
    if (sharedIncludePath) {
        cmakeOptions += " -DSHARED_INCLUDE_PATH=${sharedIncludePath}"
    }
    dir(testDir) {
        bat "cmake -S .\\ -B .\\build ${cmakeOptions} || exit 1"
    }

    dir(buildDir) {
        bat 'cmake --build . --config Debug'
    }

    def package_name = "${env.commitId}_m3dTest.tar.gz"
    dir(testDir) {
        ['run_m3d_list.bat', 'run_m3d_list_hgvps.bat', 'run_m3d_list_ph1vps.bat'].each { batFile ->
            if (fileExists(batFile)) {
                bat "copy ${batFile} .\\build"
            }
        }
        bat "tar -cvzf ${package_name} build"
        artifact.upload(env.repo, env.branch, env.commitId, "${package_name}")
    }
}

def upload_sdkGoogletest(String mtmanagementPath = '') {
    def defaultDriverPath = 'mt-management'
    mtmanagementPath = mtmanagementPath ?: defaultDriverPath

    def packageSuffix = env.packageName.tokenize('_')[-1].replace('.tar.gz', '')
    def buildType = packageSuffix.split('-')[0]
    def package_name = "${env.commitId}_sdkGoogletest_${packageSuffix}.tar.gz"
    def outDir = 'test_out'
    dir(mtmanagementPath) {
        bat "md ${outDir}"
        bat "copy /Y build\\test\\${buildType}\\*.exe ${outDir}\\"
        bat "tar -cvzf ${package_name} -C ${outDir} ."
        artifact.upload(env.repo, env.branch, env.commitId, "${package_name}")
    }
}

def signature(String version, String driverPath, String infPath = null, String infFiles = null) {
    if (!infPath || !infFiles) {
        def defaultInfDir = "${driverPath}"
        def foundInfFiles = []
        dir(defaultInfDir) {
            foundInfFiles = findFiles(glob: '*.inf')
        }
        if (foundInfFiles && foundInfFiles.size() > 0) {
            infPath = defaultInfDir
            infFiles = foundInfFiles[0].name
        } else {
            error "No .inf file found in ${defaultInfDir}!"
        }
    }

    def infFileBase = infFiles.replace('.inf', '')
    def osParam = env.driverType.contains('arm') ? '10_NI_ARM64' : '10_x64'

    bat """
        copy /Y "${WORKSPACE}\\${infPath}\\${infFiles}" "${driverPath}\\
        sed -i "s|DriverVer\\s*=\\s*[0-9]\\{1,2\\}/[0-9]\\{1,2\\}/[0-9]\\{4\\},|DriverVer = ${version},|g" "${driverPath}\\${infFileBase}.inf"
        for %%f in (${driverPath}\\*.dll) do (
            if "%%~nxf" neq "mtdxconv64.dll" if "%%~nxf" neq "mtdxconv32.dll" (
                signtool.exe sign /a /ph /fd sha256 "%%f"
            )
        )
        signtool.exe sign /a /ph /fd sha256 "${driverPath}\\*.sys"
        inf2cat.exe /uselocaltime /os:${osParam} /driver:${driverPath}
        signtool.exe sign /a /ph /fd sha256 "${driverPath}\\*.cat"
    """
}

runner.start(env.runChoice) {
    def workflow = [
        'initEnv': [closure: { initEnv() }]
    ]

    workflow['fetchCode'] = [
        closure: {
            if (env.repo?.toLowerCase() == 'm3d') {
                fetchM3dCode()
            } else {
                fetchCode()
            }
        }
    ]

    workflow['fetchDepdepPackage'] = [closure: { fetchDepdepPackage() }]

    workflow['fetchWDDMDriver'] = [closure: { fetchWDDMDriver() }]

    workflow['build'] = [closure: { build() }, setGitlabStatus: true, statusName: env.testLabel]

    workflow['upload'] = [closure: { upload() }]

    if (env.repo?.toLowerCase() == 'directstream' && !env.testLabel.contains('clang-tidy')) {
        workflow['upload_directstreamTest'] = [closure: { upload_directstreamTest() }]
    }

    if (env.repo?.toLowerCase() == 'mt-management' && !env.gitlabTargetBranch.contains('release')) {
        workflow['upload_sdkGoogletest'] = [closure: { upload_sdkGoogletest() }]
    }

    if (['wddm', 'm3d'].contains(env.repo?.toLowerCase()) && env.driverType?.toLowerCase() == 'hw') {
        workflow['upload_m3dTest'] = [closure: { upload_m3dTest() }]
    }

    runPipeline(workflow, [disablePost:true])
}
