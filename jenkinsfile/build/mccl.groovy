@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
 * packageName (String) - muPP.tar.gz
 * linuxDdkPackageUrl (String) - ''
 * mtccPackageUrl (String) - 'https://oss.mthreads.com/release-ci/computeQA/musa/newest/mtcc-nightly-x86_64-linux-gnu-ubuntu-20.04.tar.gz'
 * anacondaPackageUrl(String) - 'https://oss.mthreads.com/release-ci/computeQA/tools/musify.tar;oss/release-ci/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
 * gcov(String) - [ON | OFF]
 * exports (Multiline String) default '', split by '\n'
 * compileArgs (String) -DMUSA_ARCHS=21 -DOPENCV_INC:PATH=/usr/local/include/opencv4 -DOPENCV_LIB:PATH=/usr/local/lib -DIPP_INC=/home/<USER>/ipp/latest/include -DIPP_LIB=/home/<USER>/ipp/latest/lib/intel64 -DINSTALL_DIR=`pwd`/install
 * compileParallel (String) -16
 * packagePath(String) - ''
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/qa/mupp_compile-ubuntu-22-04:v1
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=2;requests=memory=5Gi;limits=cpu=10;limits=memory=25Gi
*/

env.repo = 'mccl'
packageName = env.packageName
env.mcclBranch = env.mcclBranch ?: env.branch

def fetchCode() {
    env.mcclCommitId = gitLib.fetchCode(env.repo, env.mcclBranch, env.mcclCommitId)
    packageName =  env.packageName ?: "${env.commitId}_${env.packageName}"
}

def envSetUp() {
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    } else {
        sh '''
            apt update ||:
            apt install libcunit1 -y ||:
        '''
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
    }
}

def build() {
    envExport = env.exports ? 'export ' + env.exports.split().join(' && export ') : ''
    // envExport = env.gcov == 'ON' ? env.exports + '&& export GCOV_TEST=ON' : envExport
    dir(env.repo) {
        sh """
            ${envExport}
            make src.build ${compileArgs} TRACE=1 -j${compileParallel}
            cd pkg/ && make -j
        """
        sh '''
            cd build/pkg/txz/
            ls *.txz | xargs tar -Jxvf && rm *.txz
            chmod 777 mccl*/install.sh
            mv mccl* mccl
        '''
    }
    sh """
        cd ${env.repo}/build/pkg/txz/
        tar czf ${env.WORKSPACE}/${packageName} ./*
    """
}

def upload() {
    def repo = env.gitlabSourceRepoName ?: env.repo
    def branch = env.gitlabSourceBranch ?: env.mcclBranch
    def commitId = env.gitlabMergeRequestLastCommit ?: env.mcclCommitId
    if (env.packagePath) {
        artifact.upload("${packageName}", env.packagePath)
    } else {
        artifact.upload(repo, branch, commitId, "${packageName}")
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'fetchCode': [closure: { fetchCode() }],
        'envSetUp': [closure: { envSetUp() }],
        'build': [closure: { build() }],
        'upload': [closure: { upload() }],
    ]

    runPipeline(workflow)
}
