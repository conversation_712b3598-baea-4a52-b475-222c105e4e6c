@Library('swqa-ci')

import org.swqa.tools.git
import groovy.transform.Field

gitLib = new git()
@Field def packageName = ''
@Field def testPackageName = ''

env.repo = 'DirectStream'

// runChoice cluster podNodeSelector podResources containerImage branch commitId triggerInfo
def fetchCode() {
    if (env.gitlabSourceRepoName == env.repo) {
        env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    } else {
        gitLib.fetchCode(env.repo, env.directstreamBranch ?: 'develop')
    }
    packageName = "${env.commitId}_${env.packageName}"
    testPackageName = "${env.commitId}_test_${env.packageName}"
}

def build() {
    def umdUrls = (env.umdPackageUrls ?: env.umdPackageUrl)?.split(',').findAll { it.trim() }
    for (umdUrl in umdUrls) {
        constants.downloadAndUnzipPackage(umdUrl, 'gr_umd_dist')
    }
    dir(env.repo) {
        def isClangTidy = env.cmd?.contains('clang_tidy')
        if (isClangTidy) {
            apt.installPackage('clang-tidy')
            sh 'clang-tidy --version'
        }
        sh "${env.cmd}"
        if (isClangTidy) {
            sh 'cat clang-tidy.*.file.log ||:'
        }
    }
}

def upload() {
    dir(env.repo) {
        sh """
            mv output directstream
            tar -zcvf ${packageName} directstream
            tar -zcvf ${testPackageName} test
        """
        if (env.uploadDbgSym?.toBoolean()) {
            catchError(buildResult: null, stageResult: null) { utils.uploadDbgSym('directstream', 'oss/debug-symbols/scripts/muti-media/upload_video_dbgsym', 'DirectStream') }
        }
        if (env.packagePath) {
            artifact.upload(packageName, env.packagePath)
            artifact.upload(testPackageName, env.packagePath)
            def formatPackagePath = env.packagePath.replaceAll('//+', '/').replaceAll('^oss/|^swci-oss/|/$', '')
            def ossPrefix = constants.genOssPrefix(formatPackagePath)
            def ossPackageUrl = "${ossPrefix}/${formatPackagePath}/${packageName}"
            sh "echo '${ossPackageUrl}' > latest.txt"
            artifact.upload('latest.txt', env.packagePath)
        } else {
            artifact.upload(env.repo, env.gitlabSourceBranch, env.commitId, packageName)
            artifact.upload(env.repo, env.gitlabSourceBranch, env.commitId, testPackageName)
        }
    }
}

runner.start(env.runChoice) {
    def workFlow = [
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    ]
    if (env.packageName) {
        workFlow['upload'] = [closure: { upload() }]
    }
    runPipeline(workFlow)
}
