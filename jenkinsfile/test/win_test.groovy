@Library('swqa-ci')

import groovy.transform.Field
import org.swqa.tools.git
import org.swqa.tools.common

//author: liya
//email: <EMAIL>

@Field List collectedResults = []
@Field def mrStatuses = []

// Get GitLab statuses once at start
def getMRStatuses() {
    try {
        retry(3) {
            sleep time: 10, unit: 'SECONDS'
            println('Waiting for 10 seconds ...')
            mrStatuses = new git().getGitlabCommitAllStatuses(env.gitlabSourceRepoName, env.gitlabMergeRequestLastCommit, env.gitlabSourceBranch)
            println '[getMRStatuses] Retrieved statuses:'
            mrStatuses.each { status ->
                println "  name: ${status.name}, status: ${status.status}"
            }
        }
    } catch (e) {
        echo "Error retrieving GitLab commit statuses: ${e.message}"
    }
}

def isVpsNode(nodeName) {
    return ['hs', 'hg', 'ph1', 'sudi', 'quyuan'].any { nodeName.contains(it) }
}

def initVps(Map config, String nodeType, String nodeName) {
    def vpsinfo = config.vpsinfo
    if (vpsinfo instanceof String) {
        vpsinfo = readJSON text: vpsinfo
    }
    if (!(vpsinfo instanceof Map) || !vpsinfo.containsKey(nodeType)) {
        error "vpsinfo missing key: ${nodeType}, current keys: ${vpsinfo instanceof Map ? vpsinfo.keySet() : vpsinfo}"
    }
    def vpsinfoParts = vpsinfo[nodeType]?.split(',') ?: []
    if (vpsinfoParts.size() < 3) {
        error "Invalid vpsinfo format: ${vpsinfoParts}"
    }

    node('Status_jump') {
        def vpsmodel = vpsinfoParts[0]
        def vpsversion = vpsinfoParts[1]
        def vpstag = vpsinfoParts[2]

        if (nodeType.contains('hg')) {
            env.hgvpstag = vpstag
        } else if (nodeType.contains('ph1')) {
            env.ph1vpstag = vpstag
        }

        startVpsNodes(nodeName, vpsversion, nodeType.split('_')[-2], vpsmodel, vpstag)
    }
}

def startVpsNodes(String nodeName, String vps_version, String chip_type, String model_type, String tag) {
    def parameters = [
        nodeName: nodeName,
        vps_version: vps_version,
        chip_type: chip_type,
        model_type: model_type,
        tag: tag
    ]

    runPipeline.runJob([
        job: 'win_start_vps_nodes',
        parameters: parameters
    ])
}

def shouldRunTest(mrStatuses, testName, statusName = null) {
    if (mrStatuses.isEmpty()) {
        echo 'No statuses found or unable to retrieve statuses from GitLab.'
        return true
    }

    def successfulStatuses = mrStatuses.findAll { it.status == 'success' && it.name.contains(testName) }

    if (successfulStatuses.isEmpty()) {
        echo "No successful statuses found containing ${testName}. Running test."
        return true
    }

    if (statusName && successfulStatuses.any { it.name.contains("${statusName}") }) {
        echo "Match found for ${statusName}. Skipping test."
        return false
}

    return true
}

def executeTestsInBatches(Map config, int n) {
    def filesToCopy = ['mtdxum32.dll', 'mtdxconv32.dll', 'mtgfxc32.dll', 'mtdxum64.dll', 'mtdxconv64.dll', 'mtgfxc64.dll', 'mtdxc64.dll', 'mtdh64.dll', 'mtdh32.dll']

    def allTests = config.testinfo.collectMany { node, tests ->
        tests.collect { test -> [node: node, test: test] }
    }

    def testBatches = allTests.groupBy { it.node }.collectEntries { nodeType, nodeTests ->
        [(nodeType): nodeTests.collate(n)]
}

    def parallelStages = [:]

    testBatches.each { nodeType, batches ->
        batches.eachWithIndex { batch, index ->
            def stageName = "${nodeType} Batch-${index + 1}"
            if (!shouldRunTest(mrStatuses, env.testName)) {
                echo "Skipping tests for ${env.testName} as tests already ran successfully."
                return
            }

            parallelStages[stageName] = {
                def nodeName = null
                def nodeSelector = env.nodeSelector ? env.nodeSelector : "CI_GFX_Win10_${nodeType}"
                node(nodeSelector) {
                    try {
                        nodeName = env.NODE_NAME
                        if (isVpsNode(nodeType)) {
                            initVps(config, nodeType, nodeName)
                        } else {
                            new common().initEnv(nodeName, "192.168.${nodeName.split('_')[-1]}")
                        }
                        sleep(time: 10, unit: 'SECONDS')
                        deleteDir()
                        if (env.driverUrl) {
                            winTest.update_driver(env.driverUrl)
                        }else {
                            winTest.update_driver_latest(env.driverType)
                        }
                        if (isVpsNode(nodeType)) {
                            dir('driver') {
                                filesToCopy.each { file ->
                                    bat "copy /Y ${file} C:\\Test\\"
                                }
                            }
                        }

                        batch.each { testInfo ->
                            def testName = testInfo.test.name
                            def statusName = testInfo.test.statusName
                            def caselist = testInfo.test.caselist
                            def timelimit = testInfo.test.timelimit
                            def driverType = testInfo.test.driverType
                            echo "testName=${testName}, statusName=${statusName}, caselist=${caselist}, timelimit=${timelimit}, driverType=${driverType}"

                            echo "Running ${statusName} on ${nodeType}"

                            if (shouldRunTest(mrStatuses, testName, statusName)) {
                                if (!testName.contains('mttrace')) {
                                    env.mailReceiver = ''
                                }
                                stage(statusName) {
                                    try {
                                        node('Status_jump') {
                                            new git().setGitlabStatus("${statusName}", 'canceled')
                                            new git().setGitlabStatus("${statusName}", 'running')
                                        }
                                        timeout(timelimit) {
                                            if (caselist) {
                                                winTest."${testName}"(caselist)
                                            } else {
                                                winTest."${testName}"()
                                            }
                                        }
                                        node('Status_jump') {
                                            new git().setGitlabStatus("${statusName}", 'success')
                                        }
                                    } catch (e) {
                                        currentBuild.result = 'FAIL'
                                        print(e)
                                        node('Status_jump') {
                                            new git().setGitlabStatus("${statusName}", 'failed')
                                        }
                                        def devOwner = env.devOwner ?: ''
                                        def testOwner = env.testOwner ?: ''
                                        currentBuild.description += 'Test case execution failed. Please check or contact SWQA'
                                        currentBuild.description += "<BR>For CI pipeline/environment issues, contact CI: ${testOwner}"
                                        currentBuild.description += "<BR>For business-related issues, contact Developer: ${devOwner}"
                                        error "${statusName} failed"
                                    }
                                }
                            } else {
                                echo "Skipping ${statusName} on ${nodeType}, already successful."
                            }
                        }
                    } catch (e) {
                        currentBuild.result = 'FAIL'
                        print(e)
                        error 'Test failed'
                    } finally {
                        try {
                            if (isVpsNode(nodeType)) {
                                initVps(config, nodeType, nodeName)
                            } else {
                                new common().initEnv(nodeName, "192.168.${nodeName.split('_')[-1]}")
                            }
                        } catch (e) {
                            echo "finalize failed: ${e.message}"
                        }
                    }
                }
            }
        }
    }

    parallel parallelStages
}

def initGitlabStatus(String statusName) {
    node('Status_jump') {
        new git().setGitlabStatus(statusName, 'canceled')
        new git().setGitlabStatus(statusName, 'running')
    }
}

runner.start(env.runChoice) {
    int numTestBatches = (env.numTestBatches ?: '5').toInteger()

    def testConfig = [:]

    def vpsinfo = env.vps ?: [:]
    testConfig.vpsinfo = vpsinfo

    def testinfo = [:]
    if (env.testEnv && env.timelimit) {
        def envs = env.testEnv.tokenize(',')
        def caselists = env.caselist ? env.caselist.tokenize(',') : []
        def partSuffix = (0 ..< 10).collect { 'part' + ((char)('A'.charAt(0) + it)) }

        envs.each { envName ->
            if (caselists && caselists.size() == 1) {
                testinfo[envName] = [[
                    name: env.testName,
                    statusName: env.statusName,
                    driverType: env.driverType,
                    caselist: caselists[0],
                    timelimit: env.timelimit as Integer
                ]]
            } else if (caselists && caselists.size() > 1) {
                testinfo[envName] = (0..<caselists.size()).collect { idx ->
                    def cl = caselists[idx]
                    def partName = idx < partSuffix.size() ? "${env.statusName}_${partSuffix[idx]}" : "${env.statusName}_part${idx + 1}"
                    [
                        name: env.testName,
                        statusName: partName,
                        caselist: cl,
                        driverType: env.driverType,
                        timelimit: env.timelimit as Integer
                    ]
                }
            } else {
                testinfo[envName] = [[
                    name: env.testName,
                    statusName: env.statusName,
                    driverType: env.driverType,
                    timelimit: env.timelimit as Integer
                ]]
            }
        }
    }
    testConfig.testinfo = testinfo

    testConfig.testinfo.each { nodeType, tests ->
        if (env.gitlabActionType == 'NOTE' && env.gitlabTriggerPhrase =~ /(?i)cirenew/) {
            tests.each { test ->
                initGitlabStatus("${test.statusName}")
            }
        }
    }
    // Get statuses at start
    getMRStatuses()

    testConfig.testinfo.each { nodeType, tests ->
        testConfig.testinfo[nodeType] = tests.findAll { test ->
            !mrStatuses.any { it.status == 'success' && it.name == test.statusName }
        }
    }

    if (!testConfig || testConfig.isEmpty()) {
        echo '[win-test] testConfig is empty, skip tests.'
        return
    }

    println '[win-test] testConfig map:'
    testConfig.each { k, v ->
        println "  ${k}: ${v}"
    }

    def workflow = [
        'executeTestsInBatches': [closure: { executeTestsInBatches(testConfig, numTestBatches) }, setGitlabStatus: true, statusName: "${env.statusName}"]
    ]

    runPipeline(workflow, [disablePost:true])
}
