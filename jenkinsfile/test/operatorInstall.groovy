@Library('swqa-ci')

/*
 * parameters
 * linuxDdkPackageUrl (String) - null
 * mtmlPackageUrl (String) - null
 * context (String)
*/

import org.swqa.tools.common

commonLib = new common()

def loadandDumpClusterConfig() {
    commonLib.loadScript('update_cluster_config.py', 'linux')
    sh "python update_cluster_config.py -c ${env.context} -l ${env.linuxDdkPackageUrl} -m ${env.mtmlPackageUrl}"
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'applyClusterconfig': [closure: { loadandDumpClusterConfig() }],
    ])
}])
