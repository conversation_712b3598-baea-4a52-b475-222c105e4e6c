@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch mtcc_test branch - default 'master'
 * linuxDdkPackageUrl (String) - default ''
 * mtccPackageUrl (String) - default ''
 * mtccLitPackageUrl (String) - default ''
 * musifyPackageUrl (String) - default 'https://oss.mthhreads.com/release-ci/computeQA/tools/musify.tar'
 * muAlgPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muAlg.tar'
 * muThrustPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muThrust.tar'
 * anacondaPackageUrl(String) - 'release-ci/computeQA/tools/musify.tar;oss/release-ci/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
 * compileArgs(String) - ''
 * gCover(boolean) - 'false'
 * exports (Multiline String) default 'usual export', split by '\n'
 * testType (String) - default 'smoke'
 * testArgs (String) -default '--device=quyuan2'
 * reportOssPath (String) - default 'oss/release-ci/computeQA/tmp/'
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - ''
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_debug:v2
*/

env.repo = 'muBLAS_cts'
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''
compileArgs = env.compileArgs ?: ''

def fetchCode() {
    env.muBLASCtsCommitId = gitLib.fetchCode(env.repo, env.muBLASCtsBranch, env.muBLASCtsCommitId)
    gitLib.fetchCode('musa_toolkit', 'master', null, [disableSubmodules: true])
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }
    constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
    sh '''
        rm /bin/sh
        ln -s /bin/bash /bin/sh
    '''
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    }
    else {
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
        dir('musa_toolkit') {
            sh 'cp -r cmake /usr/local/musa/'
        }
        constants.downloadAndUnzipPackage(env.muBLASPackageUrl)
        sh 'cd muBLAS && chmod +x install.sh . && ./install.sh'
        if (env.muBLASLtPackageUrl) {
            constants.downloadAndUnzipPackage(env.muBLASLtPackageUrl)
            sh 'cd muBLASLt && chmod +x install.sh . && ./install.sh'
        }
    }
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    musa.installMusify(env.musifyPackageUrl)
}

def runCtsTest() {
    timeout(time: env.TIMEOUT.toInteger(), unit: 'HOURS') {
        dir(env.repo) {
            sh """
                ${envExport}
                export MUSA_PORTING_PATH=${env.WORKSPACE}/musify
                git submodule update --init --recursive
                cd scripts && ./mublas_porting.sh ||:
            """
            sh """
                ${envExport}
                mkdir -p build && cd build
                cmake .. ${compileArgs}
                make -j16
            """
        }
        // test
        dir(env.repo) {
            sh """
                ${envExport}
                ${constants.genCondaActivate('mathx')}
                sync && echo 3 > /proc/sys/vm/drop_caches ||:
                export COMPILERVARS_ARCHITECTURE=intel64
                . /opt/intel/oneapi/setvars.sh
                export MUSA_PORTING_PATH=${env.WORKSPACE}/musify
                python run_test.py ${env.testArgs} ||:
            """
        }
    }
}

def checkResult() {
    dir(env.repo) {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    dir(env.repo) {
        sh 'tar -czvf muBLAS_cts_allure_result.tar.gz test-report'
        artifact.uploadTestReport('muBLAS_cts_allure_result.tar.gz', env.reportOssPath)
    }
}

runner.start(env.runChoice, [
    main: {
        runPipeline([
            'checkout': [closure: { fetchCode() }],
            'setup in docker': [closure: { setUpinDocker() }],
            'muBLAS test': [closure: { runCtsTest() }],
        ], [disablePre: true, disablePost: true])
    },
    post: {
        runPipeline([
            'upload result': [closure: {
                catchError(stageResult: 'FAILURE') {
                        uploadTestResult()
                }
            }
            ],
            'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        ], [disablePre: true])
    },
    pre: {
        runPipeline([
            'setup pre': [closure: { setUpOnNode() }],
        ], [disablePost: true])
    }
])
