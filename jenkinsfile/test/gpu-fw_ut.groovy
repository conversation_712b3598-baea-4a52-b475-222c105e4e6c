@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

env.repoName = 'gpu-fw'
isAccess = env.isAccess == 'true'
coverityPathInDocker = '/home/<USER>'
coverityBinPathInDocker = '/home/<USER>/bin'
iDir = "/data/samba/sast/idir-${env.JOB_NAME}-${env.BUILD_NUMBER}/release_build_${env.repoName}"

env.mountParms = "-v ${coverity.coverityPath}:/${coverityPathInDocker} -v /data:/data"

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repoName, env.branch, env.commitId)
}

def continuousIntegration() {
    dir(env.repoName) {
        if (isAccess) {
            sh 'bash ./ci_build.sh'
        }
        sh "${coverityBinPathInDocker}/cov-build --dir ${iDir} ./release_build.sh"
    }
}

def coverityAnalyze() {
    dir(env.repoName) {
        try {
            coverity.analyze(coverity.coverityBinPath, iDir, isAccess, env.WORKSPACE)
            def targetBranch = env.gitlabTargetBranch ?: 'develop'
            if (isAccess) {
                coverity.checkReport(coverity.coverityBinPath, iDir, '', "${env.repoName}_${targetBranch}-x64_build")
            } else {
                coverity.upstream(coverity.coverityBinPath, iDir, '', "${env.repoName}_${targetBranch}-x64_build", env.commitId)
            }
        } catch (exc) {
            if (isAccess) {
                commonLib.publishHTML('html', 'index.html', "Build Coverity Results for ${env.repoName}")
            }
            throw exc
        }
    }
}

runner.start(env.runChoice, {
    def workflow = [
        'checkout': [ closure: { fetchCode() } ],
        'run test': [ closure: { continuousIntegration() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    ]
    runPipeline(workflow)
})
