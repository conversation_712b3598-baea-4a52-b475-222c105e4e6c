@Library('swqa-ci')

import groovy.transform.Field

env.repo = 'DirectStream'
@Field String codecDir = ''

def setUpOnNode() {
    ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl, false) {
        if (env.mediaDriverPackageUrl) { ddk.installLinuxDdk(env.mediaDriverPackageUrl) }
    }
    def latestDsUrl = "http://oss.mthreads.com/release-ci/DirectStream/${env.gitlabTargetBranch}/linux/latest.txt"
    def latestDsPackageUrl = utils.runCommandWithStdout("curl --insecure ${latestDsUrl}")
    env.directStreamPackageUrl = env.directStreamPackageUrl ?: latestDsPackageUrl
    env.directStreamTestPackageUrl = env.directStreamTestPackageUrl ?: latestDsPackageUrl.replaceAll(/_directstream\.tar\.gz$/, '_test_directstream.tar.gz')

    constants.downloadAndUnzipPackage(env.directStreamPackageUrl)
    constants.downloadAndUnzipPackage(env.directStreamTestPackageUrl)
    sh 'mv directstream output'
}

def codecTest() {
    List envs = env.exports ? env.exports.split(';') : []
    def envExport = envs ? 'export ' + envs.join(' && export ') : ''
    codecDir = env.testScript =~ 'dec' ? 'test/Decode' :
               env.testScript =~ 'enc' ? 'test' :
               env.testScript =~ 'capture' ? 'test/Capture' : 'test'
    dir(codecDir) {
        sh """
            ${envExport}
            ${env.testScript}
        """
    }
}

def checkResult() {
    dir(codecDir) {
        def logName = env.testScript =~ 'capture' ? 'capture_result.txt' : 'result.txt'
        if (fileExists(logName)) {
            def result = sh(script: "cat ${logName}", returnStdout: true).trim()
            if (result != '0') {
                sh 'cat log.txt || true'
                error('test failed')
            } else {
                sh 'cat log.txt || true'
            }
        }
    }
}

runner.start(env.runChoice, {
    def workflow = [
        'setUp': [closure: { setUpOnNode() }],
        'codec test': [closure: { codecTest() }, maxWaitTime: [time: env.testTimeout, unit: 'MINUTES']],
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: env.testLabel]
    ]
    runPipeline(workflow)
})
