@Library('swqa-ci')

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
 * cmd (String) - ./build.sh -d ./install -j 16 -c mp21
 * exports (Multiline String) default 'usual export', split by '\n'
 * dependcy (Multiline String) - default 'musolver_data', split by ';
 * musaToolkitsPackageUrl (String) - default 'musatoolkit url'
 * muDNNPackageUrl (String) - ''
 * condaPackageUrl (String) - 'https://oss.mthreads.com/release-ci/computeQA/ai-rely-pkg/miniforge/miniforge_mtdnn.tar.gz'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd-uos:v26
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=9;requests=memory=96Gi;limits=cpu=18;limits=memory=96Gi
*/

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()
env.repo = 'ComputeAsmKern'
env.muDNN_cts_repo = 'muDNN_cts'
// env.ref, env.head_commit are from webhook, triggered by code merge
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''
generateTestUrl = env.generateTestUrl ? env.generateTestUrl.split(';') : []

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    env.mudnn_ctsCommitId = gitLib.fetchCode(env.muDNN_cts_repo, env.mudnn_ctsBranch, env.mudnn_ctsCommitId)
    gitLib.fetchCode('musa_toolkit', 'master', null, [disableSubmodules: true])
}

def envSet() {
    sh 'apt install ninja-build ||:'
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }
    constants.downloadAndUnzipPackage(env.condaPackageUrl, '/home/<USER>')
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    }
    else {
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
        dir('musa_toolkit') {
            sh 'cp -r cmake /usr/local/musa/'
        }
    }
}
def runTest() {
    dir("${env.muDNN_cts_repo}/asmkern_pytest") {
        try {
            sh """
                ${envExport}
                ${constants.genCondaActivate('mtdnn_mtgpu')}
                pytest . -v -m ${env.testMark} --alluredir test-report ||:
                tar -czf mudnn_cts_allure_result.tar.gz test-report/
            """
        } catch (exc) {
            throw new Exception('test failed!')
        } finally {
            sh '''
                dmesg -T || :
            '''
        }
    }
}
def checkResult() {
    dir("${env.WORKSPACE}/${env.muDNN_cts_repo}/asmkern_pytest") {
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    oss.install()
    generateTestUrl.each {
        oss.cp("${env.WORKSPACE}/${env.muDNN_cts_repo}/asmkern_pytest/mudnn_cts_allure_result.tar.gz", it)
        currentBuild.description += "<br>generate_pkg:${it}/mudnn_cts_allure_result.tar.gz".replaceAll('//', '/')
    }
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'env-setup': [closure: { envSet() }],
        'runTest': [closure: { runTest() }],
    ], [disablePost: true])
}, post: {
    runPipeline([
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
        'upload result': [closure: { uploadTestResult() } ]
    ], [disablePre: true])
}, pre: {
    if (env.runChoice == 'node' && env.installDDKonHost == 'true') {
        stage('install ddk') {
            // restart machine
            commonLib.reboot(env.NODE_NAME)
            // env recovery
            commonLib.recoverEnv()
            ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
        }
    }
}])
