@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * musaCtsBranch - default 'm3d_master'
 * musaCtsCommitId - default 'm3d_master'
 * mtccBranch - default 'm3d_master'
 * mtccCommitId - default 'm3d_master'
 * linuxDdkPackageUrl (String) - default ''
 * mtccPackageUrl (String) - default ''
 * musifyPackageUrl (String) - default ''
 * musaAsmReleasePackageUrl (String) -default ''
 * condaPackageUrl (String) - default ''
 * AmodlePackageUrl (String) - default ''
 * CmodlePackageUrl (String) - default ''
 * muArgPackageUrl (String) - default ''
 * muthrustPackageUrl (String) - default ''
 * gpuArch (String) - default ''
 * qemuVersion (String) - default ''
 * qemuModel (String) - default ''
 * exports (Multiline String) default 'usual export', split by '\n'
 * testType (String) - default 'smoke'
 * testMark (String) - default ''
 * reportOssPath (String) - default 'oss path'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
*/

List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''

def fetchCode() {
    env.musaCtsCommitId = gitLib.fetchCode('musa_cts', env.musaCtsBranch, env.musaCtsCommitId)
    // env.mtccTestCommitId = gitLib.fetchCode('mtcc_test', env.mtccTestBranch, env.mtccTestCommitId)
    sh """
        cp /home/<USER>/${env.vpsImg} ${env.WORKSPACE}
    """
}

def buildDiagsys() {
    submoduleMaps = constants.getLinuxDdkSubmoduleCommitInfo(env.gitlabSourceBranch, gitlabMergeRequestLastCommit, false)
    runnerHttp.runJob('diagsys_build', ['Shared_Include_Commit_Id':submoduleMaps['shared_include']])
}

def compileMusaAndCases() {
    try {
        docker.image(env.dockerImage).inside('-i -u 0:0 -v /home/<USER>/home/<USER>') {
            apt.updateAptSourcesList()
            sh '''
                pip install ahocorapy
                apt-get install -y pciutils
            '''

            ddk.installLinuxDdk(env.linuxDdkPackageUrl)

            // 安装MUSA-Runtime
            if (env.musaRuntimePackageUrl) {
                musa.installMusaRuntime(env.musaRuntimePackageUrl)
            }

            installDependency(['mtcc': env.mtccPackageUrl])
            def dependencies = ['mtcc': env.mtccPackageUrl]
            installDependency(dependencies)

            constants.downloadAndUnzipPackage(env.musaAsmReleasePackageUrl)
            dir('musa_asm/build/bin') {
                sh 'cp musaasm /usr/local/musa/bin/'
            }
            dir('muAlg') {
                constants.downloadAndUnzipPackage(env.muArgPackageUrl)
                sh '''
                    cd package
                    ls *.deb | xargs -n1 dpkg -i
                '''
            }
            dir('muthrust') {
                constants.downloadAndUnzipPackage(env.muthrustPackageUrl)
                sh '''
                    cd package
                    ls *.deb | xargs -n1 dpkg -i
                '''
            }

            dir('pkg') {
                constants.downloadAndUnzipPackage(env.linuxDdkPackageUrl)
                constants.downloadAndUnzipPackage(env.mtccPackageUrl)
                if (env.musaRuntimePackageUrl) {
                    constants.downloadAndUnzipPackage(env.musaRuntimePackageUrl)
                }
            }

            musa.installMusify(env.musifyPackageUrl)
            dir('musa_cts') {
                sh '''
                    rm -rf .git* ||:
                    rm -rf mtcc/mtcc_test/ ||:
                '''
            }

            if (env.AmodlePackageUrl) {
                dir("${env.WORKSPACE}") {
                    sh """
                        mc alias set oss https://oss.mthreads.com mtoss mtoss123
                        mkdir -p ${env.WORKSPACE}/soc_model/release_mode/amodel/ph1s
                        mc cp -r ${env.AmodlePackageUrl} ${env.WORKSPACE}/soc_model/release_mode/amodel/ph1s
                    """
                }
            }
            if (env.CmodlePackageUrl) {
                dir("${env.WORKSPACE}") {
                    sh """
                        mc alias set oss https://oss.mthreads.com mtoss mtoss123
                        mc cp -r ${env.CmodlePackageUrl}arch_DOG_release_binary.tar.gz ./
                        tar -xzvf arch_DOG_release_binary.tar.gz >/dev/null
                        mkdir -p ${env.WORKSPACE}/soc_model/release_mode/cmodel/ph1s/
                        cd ci_env/transif_binary/
                        cp param_configuration.conf  ${env.WORKSPACE}
                        cp *.so* qemu-system-riscv64 ${env.WORKSPACE}/soc_model/release_mode/cmodel/ph1s/ -rf
                    """
                }
            }

            dir('musa_cts') {
                // 编译musa_cts api cases
                sh """
                    export MUSA_PORTING_PATH=${env.WORKSPACE}/musify
                    export PATH=/usr/local/musa/bin:\$PATH
                    export LD_LIBRARY_PATH=/usr/local/musa/lib/:\${LD_LIBRARY_PATH}
                    export MTGPU_ARCH=${env.gpuArch}
                    cmake -B build .
                    cmake --build build -j48
                """

                // 编译musa_cts mtcc cases
                sh """
                    export TEST_TYPE=${env.testType}
                    export RUN_TYPE=compile
                    export MTGPU_ARCH=${env.gpuArch}
                    export COMPILE_EXTRA_ARGS=\"-mllvm -mtgpu-force-wave32=true\"
                    export LD_LIBRARY_PATH=/usr/local/musa/lib:/usr/local/lib/musa/lib/x86_64-linux-gnu/:\$LD_LIBRARY_PATH
                    export PATH=/usr/local/musa/bin:\$PATH
                    cd pytest/test_mtcc
                    mkdir -p ${env.WORKSPACE}/musa_cts/log
                    /home/<USER>/miniforge/envs/mathx/bin/pytest -v -n 24 test_musa_mtcc.py test_musa_mtcc_fuzz.py ||:
                """
            }
        }
    } catch (err) {
        // 容器退出后自动清理失败（Jenkins 报 kill container failed）时跳过
        if (err.message?.contains('Failed to kill container')) {
            echo " 容器清理失败，跳过：${err.message}"
        } else {
            // 其它报错继续抛出，不隐藏
            throw err
        }
    }
    sh """
        docker rm -f `docker ps -a | awk '/Exited|Create|Removal/ {print \$NF}'`  2>/dev/null ||:
        docker rmi `docker images | grep none | awk '{print \$3}'` ||:
    """
    sh """
        tar -zcvf musa_cts_${env.BUILD_ID}.tar.gz musa_cts/ >/dev/null
        mv musa_cts_${env.BUILD_ID}.tar.gz pkg/
        cd pkg
        mv *ddk*.tar.gz linux-ddk_${env.BUILD_ID}.tar.gz
    """
    oss.install()
    oss.cp('pkg/*.tar.gz', "oss/release-ci/computeQA/tmp/test.vps_ph1s/${env.BUILD_ID}/")
}

def runCtsTest() {
    def qemuModel = env.AmodlePackageUrl ? 'amodel' : 'cmodel'
    def sshPort = "1${('111' + env.BUILD_ID)[-4..-1]}"
    def mttraceTestExport = env.mttraceTestExport == 'true' ? 'export MTTRACE_ENABLE=1' : 'export MTTRACE_ENABLE=0'
    def modelPath = env.AmodlePackageUrl ? "-aml ${env.AmodlePackageUrl}" : "-cml ${env.CmodlePackageUrl}"
    sh """
        ${mttraceTestExport}
        export TEST_TYPE=${env.testType}
        export MAX_TIME_LIMIT=`echo "1800" | bc -q`
        cd ${env.WORKSPACE}/musa_cts/vps_test
        /home/<USER>/miniforge/envs/mathx/bin/python -u gen_file_for_ATF_ph1s.py -qm ${qemuModel} -wd ${env.WORKSPACE} -crd allure_result_vps_musa -qid ${env.WORKSPACE} -qi test_compute_v3_no_cuda.img -dia \"${env.driverInstallArgs}\" -md ${env.WORKSPACE}/soc_model -mlcf ${env.WORKSPACE}/mtcc_test/vps_test/test_cfg_for_ATF_mtcc.csv -tga ${env.gpuArch} -mlf 0  -pif 0 -optf 0 -tcf test_cfg_for_ATF_ph1s.csv -vn vps_test_musa_ph1s_img_${env.BUILD_ID} -vp ${sshPort} ${env.runnerPythonCmd} ${modelPath}
    """

    def resultsDirMusaCts = "${env.WORKSPACE}/allure_result_vps_musa"
    def reportNameMusaCts = "ph1s_${qemuModel}_vps_allure_report"
    if (fileExists(resultsDirMusaCts)) {
        commonLib.allure(resultsDirMusaCts, reportNameMusaCts)
    } else {
        echo "Warning: ${resultsDirMusaCts} not found"
    }
}

runner.start(env.runChoice, {
    def workflow = ['checkout': [closure: { fetchCode() }]]
    if (env.gitlabTargetRepoName == 'linux-ddk' && env.testLabel =~ 'mttrace') {
        workflow['build diagsys'] = [closure: { buildDiagsys() }]
    }
    // workflow['setup on node'] = [closure: { setUpOnNode() }]
    workflow['setup in docker'] = [closure: { compileMusaAndCases() }]
    workflow['run cts test'] = [closure: { runCtsTest() }, setGitlabStatus: true, statusName: "${env.testLabel}"]
    runPipeline(workflow)
})
