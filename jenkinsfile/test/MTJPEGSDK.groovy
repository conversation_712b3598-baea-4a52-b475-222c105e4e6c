@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

// runChoice nodeLabel repo branch commitId ddkBranch linuxDdkPackageUrl mtJpegPackageUrl

def installDriver() {
    ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl, false) {
        if (env.mtJpegPackageUrl) {
            constants.downloadAndUnzipPackage(env.mtJpegPackageUrl)
            sh 'cd output; ./install.sh'
        }
    }
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
}

def runTest() {
    gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
    commonLib.loadScript('install_libva.sh', 'linux')
    sh """
        apt --fix-broken install -y ||:
        export LIBVA_DRIVER_NAME=mtgpu
        sh install_libva.sh ${env.WORKSPACE}/libva-2.15 /usr/lib/`arch`-linux-gnu/dri 2.15
    """
    dir("${env.repo}/test") {
        sh "./test.sh -p ${env.WORKSPACE}/output/x86_64/bin/"
        result = utils.runCommandWithStdout('cat result_ci.txt')
        print(result)
        if (result.toInteger() != 0) {
            error 'test fail!'
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'install driver': [closure: { installDriver() }, maxWaitTime: [time: 15, unit: 'MINUTES']],
        'mtjpegsdk test': [closure: { runTest() }, setGitlabStatus: true, statusName: env.testLabel]
    ]
    runPipeline(workflow)
}
