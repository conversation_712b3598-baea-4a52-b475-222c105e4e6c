@Library('swqa-ci')

/*
 * parameters
 * branch (String) - develop
 * commitId (String) - ''
 * cmd (String) - ./build.sh -d ./install -j 16 -c mp21
 * exports (Multiline String) default 'usual export', split by '\n'
 * dependcy (Multiline String) - default 'musolver_data', split by ';
 * musaToolkitsPackageUrl (String) - default 'musatoolkit url'
 * muDNNPackageUrl (String) - ''
 * condaPackageUrl (String) - 'https://oss.mthreads.com/release-ci/computeQA/ai-rely-pkg/miniforge/miniforge_mtdnn.tar.gz'
 * runChoice (Choice) - pod [node | pod]
 * nodeLabel (Choice) - Linux_build
 * cluster (String) - shfarm
 * containerImage (String) - sh-harbor.mthreads.com/build-env/gr-umd-uos:v26
 * podNodeSelector (String) - mt=In=buildserver
 * podResources (String) - requests=cpu=9;requests=memory=96Gi;limits=cpu=18;limits=memory=96Gi
*/

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()
env.repo = 'muDNN_cts'
env.muDNN_repo = 'muDNN'
// env.ref, env.head_commit are from webhook, triggered by code merge
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''
generateTestUrl = env.generateTestUrl ? env.generateTestUrl.split(';') : []

def fetchCode() {
    env.mudnn_ctsCommitId = gitLib.fetchCode(env.repo, env.mudnn_ctsBranch, env.mudnn_ctsCommitId)
    gitLib.fetchCode(env.muDNN_repo, env.mudnnBranch)
    gitLib.fetchCode('musa_toolkit', 'master', null, [disableSubmodules: true])
}

def envSet() {
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }
    constants.downloadAndUnzipPackage(env.condaPackageUrl, '/home/<USER>')
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(constants.ossPathToUrl(env.musaToolkitsPackageUrl))
    }
    else {
        if (env.musaRuntimePackageUrl) {
            musa.installMusaRuntime(env.musaRuntimePackageUrl)
        }

        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
        dir('musa_toolkit') {
            sh 'cp -r cmake /usr/local/musa/'
        }
    }
    dir(env.muDNN_repo) {
        sh """
            ${constants.genCondaActivate('mtdnn_mtgpu')}
            dvc remote modify --local mudnn_golden access_key_id "mtoss"
            dvc remote modify --local mudnn_golden secret_access_key "mtoss123"
            dvc pull
        """
    }
}
def installMudnn() {
    if (env.muDNNPackageUrl) {
        env.muDNNPackageUrl = constants.ossPathToUrl(env.muDNNPackageUrl)
        sh """
            wget ${env.muDNNPackageUrl}
            tar -xf ${env.muDNNPackageUrl.split('/')[-1]}
            cd mudnn
            ./install_mudnn.sh -i
        """
        envExport += '&& export MUDNN_LIB=/usr/local/musa/lib/ && export MUDNN_HEADERS=/usr/local/musa/include/'
    }
    else if (env.musaToolkitsPackageUrl) {
        def baseDir = "${env.musaToolkitsPackageUrl}".substring(0, "${env.musaToolkitsPackageUrl}".lastIndexOf('/'))
        baseDir = (constants.urlToOSSPath(baseDir) + '/others')
        def isPh1 = sh(returnStdout: true, script: 'lspci -n|grep 1ed5:0400 || true').trim()
        def matchStr = "mc ls ${baseDir}|grep -i mudnn"
        if (isPh1) {
            matchStr += '|grep -i ph1'
        } else {
            matchStr += '|grep -vi ph1'
        }
        def mudnn_pkg = sh(returnStdout: true, script: "${matchStr} |awk -F ' ' '{print \$NF}'").trim()
        env.muDNNPackageUrl = constants.ossPathToUrl("${baseDir}/${mudnn_pkg}")
        sh """
            wget ${env.muDNNPackageUrl}
            tar -xf ${env.muDNNPackageUrl.split('/')[-1]}
            cd mudnn
            ./install_mudnn.sh -i
        """
        envExport += '&& export MUDNN_LIB=/usr/local/musa/lib/ && export MUDNN_HEADERS=/usr/local/musa/include/'
    }
    else {
        print('no mudnn pkg url do nothing')
    }
}
def runTest() {
    dir(env.muDNN_repo) {
        sh"""
            ${constants.genCondaActivate('mtdnn_mtgpu')}
            cd tests
            ./build_all.sh -o -p
        """
        envExport += "&& export PYTHONPATH=${env.WORKSPACE}/muDNN/tests/python:${env.WORKSPACE}/muDNN/tests/build"
        print(envExport)
    }
    dir(env.repo) {
        try {
            sh """
                ${envExport}
                ${constants.genCondaActivate('mtdnn_mtgpu')}
                python run_test.py -m ${env.test_mark}
                tar -czf mudnn_cts_allure_result.tar.gz test-report/ ||:
                tar -czf mudnn_perf_score.tar.gz log/mudnn_perf/ ||:
            """
        } catch (exc) {
            throw new Exception('test failed!')
        } finally {
            sh '''
                dmesg -T || :
            '''
        }
    }
}
def checkResult() {
    dir("${env.WORKSPACE}/${env.repo}") {
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    dir(env.repo) {
        artifact.uploadTestReport('mudnn_perf_score.tar.gz', env.reportOssPath)
    }
}

runner.start(env.runChoice, [main: {
    runPipeline([
        'checkout': [closure: { fetchCode() }],
        'env-setup': [closure: { envSet() }],
        'install-mudnn': [closure: { installMudnn() }],
        'runTest': [closure: { runTest() }],
    ], [disablePost: true])
}, post: {
    runPipeline([
        'upload result': [closure: { uploadTestResult() } ],
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
    ], [disablePre: true])
}, pre: {
    if (env.runChoice == 'node' && env.installDDKonHost == 'true') {
        stage('install ddk') {
            // restart machine
            commonLib.reboot(env.NODE_NAME)
            // env recovery
            commonLib.recoverEnv()
            ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
        }
    }
}])
