@Library('swqa-ci')

import org.swqa.tools.git

gitLib = new git()

env.repo = 'DirectStream'

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId, [updateBuildDescription: true])
}

def build() {
    apt.installPackage('freeglut3-dev libglew-dev dos2unix')
    List envs = env.exports ? env.exports.split(';') : []
    def envExport = envs ? 'export ' + envs.join(' && export ') : ''
    credentials.runWithCredential('SSH_GITLAB') {
        dir(env.repo) {
            sh """
                ./download_gr_umd_dist.sh
                ${envExport}
                sed -i 's/https:\\/\\/github.mthreads.com\\/mthreads/************************:sw/g' ./DirectStream_Build_Linux.sh
                ./DirectStream_Build_Linux.sh -v 2.20
            """
        }
    }
}

def setUpOnNode() {
    env.linuxDdkPackageUrl = env.linuxDdkPackageUrl ?: constants.genLatestPackageUrl('linux-ddk', 'master', 'ddk2.0.deb')
    ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl, false) {
        if (env.musaToolkitPackageUrl) { musa.installMusaToolkits(env.musaToolkitPackageUrl) }
    }
    currentBuild.description += "linuxDdkPackageUrl: ${env.linuxDdkPackageUrl} <br>"
}

def codecTest(String script) {
    def codec = script.split('_')[0].trim()
    try {
        sh """
            export LIBVA_DRIVER_NAME=mtgpu
            export PATH=/usr/local/mt_vaapi/bin:\$PATH
            export LD_LIBRARY_PATH=/usr/local/mt_vaapi/lib:/usr/local/mt_vaapi/lib/x86_64-linux-gnu/:\$LD_LIBRARY_PATH
            chmod +x ./${script} && ./${script}
        """
    } catch (exc) {
        error("${exc}")
    } finally {
        if (fileExists('result.txt')) {
            sh "mv result.txt ${codec}_result.txt; mv log.txt ${codec}_log.txt ||:"
        }
    }
}

def runTest() {
    dir("${env.repo}/test/musa") {
        sh 'dmesg -wT > dmesg_ci_test.txt &'
        codecTest('enc_ci_musa.sh')
        codecTest('dec_ci.sh')
    }
}

def checkResult() {
    dir("${env.repo}/test/musa") {
        sh 'cat enc_log.txt tmp.log ||:'
        if (fileExists('enc_result.txt') && fileExists('dec_result.txt')) {
            def encRes = sh(script: 'cat enc_result.txt', returnStdout: true).trim()
            def decRes = sh(script: 'cat dec_result.txt', returnStdout: true).trim()
            if (encRes != '0' || decRes != '0') {
                currentBuild.result = 'FAILURE'
                error("${env.testLabel} failed")
            }
        }
    }
}

def uploadTestResult() {
    dir("${env.repo}/test/musa") {
        sh """
            dmesg -T
            tar -zcvf dmesg_ci_test_${BUILD_ID}.tar.gz dmesg_ci_test.txt
        """
        def reportOssPath = env.reportOssPath ?: "swci-oss/sw-pr/${env.repo}/${env.JOB_NAME}/${env.BUILD_NUMBER}"
        artifact.uploadTestReport("dmesg_ci_test_${BUILD_ID}.tar.gz", reportOssPath)
    }
}

runner.start(env.runChoice, {
    def workflow = [
        'setUp': [closure: { setUpOnNode() }],
        'checkout': [closure: { fetchCode() }],
        'build': [closure: { build() }],
        "${env.testLabel}": [closure: { runTest() }, maxWaitTime: [time: env.testTimeout, unit: 'MINUTES']],
        'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: env.testLabel],
        'upload result': [closure: { uploadTestResult() }]
    ]
    runPipeline(workflow)
})
