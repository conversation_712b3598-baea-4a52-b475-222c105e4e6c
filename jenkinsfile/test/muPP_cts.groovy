@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch mtcc_test branch - default 'master'
 * linuxDdkPackageUrl (String) - default ''
 * mtccPackageUrl (String) - default ''
 * mtccLitPackageUrl (String) - default ''
 * musifyPackageUrl (String) - default 'https://oss.mthhreads.com/release-ci/computeQA/tools/musify.tar'
 * muAlgPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muAlg.tar'
 * muThrustPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muThrust.tar'
 * anacondaPackageUrl(String) - 'release-ci/computeQA/tools/musify.tar;oss/release-ci/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
 * compileArgs(String) - ''
 * gCover(boolean) - 'false'
 * exports (Multiline String) default 'usual export', split by '\n'
 * testType (String) - default 'smoke'
 * testArgs (String) -default '--device=quyuan2'
 * reportOssPath (String) - default 'oss/release-ci/computeQA/tmp/'
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - ''
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_debug:v2
*/

env.repo = 'muPP_cts'
env.repomuPPTest = 'muPPTest'
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    env.muppTestCommitId = gitLib.fetchCode(env.repomuPPTest, env.muppTestBranch, env.muppTestCommitId)
    gitLib.fetchCode('musa_toolkit', 'master', null, [disableSubmodules: true])
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }
    constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
    if (env.musaToolkitsPackageUrl) {
        musa.installMusaToolkits(env.musaToolkitsPackageUrl)
    }
    else {
        def dependencies = ['mtcc': env.mtccPackageUrl]
        installDependency(dependencies)
        dir('musa_toolkit') {
            sh 'cp -r cmake /usr/local/musa/'
        }
        constants.downloadAndUnzipPackage(env.muPPPackageUrl)
        sh 'cd muPP && chmod -R 777 . && ./install.sh ||:'
    }
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    musa.installMusify(env.musifyPackageUrl)
}

def runCtsTest() {
    timeout(time: env.TIMEOUT.toInteger(), unit: 'HOURS') {
        dir(env.repomuPPTest) {
            sh '''
                mkdir build && cd build
                cmake -DGTEST_ROOT=/home/<USER>/miniforge/envs/mathx  -DIPP_ROOT=/home/<USER>/ipp/latest ..
                make -j16
            '''
        }
        dir(env.repo) {
            sh '''
                wget --no-check-certificate https://oss.mthreads.com/release-ci/computeQA/tools/googletest.tar.gz
                tar -xzf googletest.tar.gz
                cd googletest  && mkdir build && cd build && cmake .. && make -j8 && make install
            '''
            sh """
                ${constants.genCondaActivate('mathx')}
                ${envExport}
                git submodule update --init --recursive
                mkdir -p build && cd build
                cmake .. ${buildModule} && make -j16
            """
        }
        // test
        dir(env.repo) {
            sh """
                ${envExport}
                sync && echo 3 > /proc/sys/vm/drop_caches ||:
                export MUSA_PORTING_PATH=${env.WORKSPACE}/musify
                ${constants.genCondaActivate('mathx')}
                python run_test.py ${testArgs}
            """
        }
    }
}

def checkResult() {
    dir(env.repo) {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    dir(env.repo) {
        sh 'tar -czvf mupp_cts_allure_result.tar.gz test-report'
        artifact.uploadTestReport('mupp_cts_allure_result.tar.gz', env.reportOssPath)
    }
}

runner.start(env.runChoice, [
    main: {
        runPipeline([
            'checkout': [closure: { fetchCode() }],
            'setup in docker': [closure: { setUpinDocker() }],
            'muPP test': [closure: { runCtsTest() }],
        ], [disablePre: true, disablePost: true])
    },
    post: {
        runPipeline([
            'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
            'upload result': [closure: { uploadTestResult() }]
        ], [disablePre: true])
    },
    pre: {
        runPipeline([
            'setup pre': [closure: { setUpOnNode() }],
        ], [disablePost: true])
    }
])
