@Library('swqa-ci')

import org.swqa.tools.git
import org.swqa.tools.common

gitLib = new git()
commonLib = new common()

/*
 * parameters
 * branch mtcc_test branch - default 'master'
 * linuxDdkPackageUrl (String) - default ''
 * mtccPackageUrl (String) - default ''
 * mtccLitPackageUrl (String) - default ''
 * musifyPackageUrl (String) - default 'https://oss.mthhreads.com/release-ci/computeQA/tools/musify.tar'
 * muAlgPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muAlg.tar'
 * muThrustPackageUrl (String) - default 'https://oss.mthreads.com/release-ci/computeQA/mathX/newest/muThrust.tar'
 * anacondaPackageUrl(String) - 'release-ci/computeQA/tools/musify.tar;oss/release-ci/computeQA/ai-rely-pkg/miniforge/miniforge_mathx.tar.gz'
 * compileArgs(String) - ''
 * gCover(boolean) - 'false'
 * exports (Multiline String) default 'usual export', split by '\n'
 * testType (String) - default 'smoke'
 * testArgs (String) -default '--device=quyuan2'
 * reportOssPath (String) - default 'oss/release-ci/computeQA/tmp/'
 * runChoice (Choice) - node [node | pod]
 * nodeLabel (Choice) - ''
 * containerImage (String) - sh-harbor.mthreads.com/qa/musa_debug:v2
*/

env.repo = 'muThrust_cts'
List envs = env.exports ? env.exports.split(';') : []
envExport = envs ? 'export ' + envs.join(' && export ') : ''

def fetchCode() {
    env.commitId = gitLib.fetchCode(env.repo, env.branch, env.commitId)
    gitLib.fetchCode('musa_toolkit', 'master', null, [disableSubmodules: true])
}

def setUpOnNode() {
    // install linuxDdk full pkgs and insmod mtgpu
    if (env.runChoice == 'node') {
        ddk.installLinuxDdkAndSetup(env.linuxDdkPackageUrl)
    }
}

def setUpinDocker() {
    sh '''
        apt update ||:
        apt install dkms -y ||:
    '''
    if (env.linuxDdkPackageUrl) {
        ddk.installLinuxDdk(env.linuxDdkPackageUrl)
    }
    constants.downloadAndUnzipPackage(env.anacondaPackageUrl, '/home/<USER>')
    def dependencies = ['mtcc': env.mtccPackageUrl]
    installDependency(dependencies)
    if (env.musaRuntimePackageUrl) {
        musa.installMusaRuntime(env.musaRuntimePackageUrl)
    }
    musa.installmuAlg(env.muAlgPackageUrl)
    musa.installmuThrust((env.muThrustPackageUrl))
    dir('musa_toolkit') {
        sh 'cp -r cmake /usr/local/musa/'
    }
    if (env.gCover == 'true') {
        dir(env.repo) {
            sh '''
                mkdir gcov
                cp -r /usr/local/musa/include/thrust  ./gcov/
                cp -r /usr/local/musa/include/cub ./gcov/
            '''
        }
    }
}

def runCtsTest() {
    timeout(time: env.TIMEOUT.toInteger(), unit: 'HOURS') {
        dir(env.repo) {
            def GCOV_TEST = env.gCover == 'true' ? 'export GCOV_TEST=ON' : ''
            sh """
                ${GCOV_TEST}
                mkdir build
                cd build
                cmake .. ${env.compileArgs}
                make -j8
            """
        }
        // test
        dir(env.repo) {
            sh """
                ${envExport}
                ${constants.genCondaActivate('mathx')}
                python run_test.py ${testArgs}
            """
        }
    }
}

def checkResult() {
    dir(env.repo) {
        //python run_test.py generate test-report dir include allure report
        commonLib.allure('test-report')
    }
}

def uploadTestResult() {
    dir(env.repo) {
        sh 'tar -czvf muthrust_cts_allure_result.tar.gz test-report'
        artifact.uploadTestReport('muthrust_cts_allure_result.tar.gz', env.reportOssPath)
        if (env.gCover == 'true') {
            sh 'tar -czvf muthrust_cts_test_llvm_coverage.tar.gz llvm_cov_data'
            artifact.uploadTestReport('muthrust_cts_test_llvm_coverage.tar.gz', env.reportOssPath)
        }
    }
}

runner.start(env.runChoice, [
    main: {
        runPipeline([
            'checkout': [closure: { fetchCode() }],
            'setup in docker': [closure: { setUpinDocker() }],
            'muThrust test': [closure: { runCtsTest() }],
        ], [disablePre: true, disablePost: true])
    },
    post: {
        runPipeline([
            'check result': [closure: { checkResult() }, setGitlabStatus: true, statusName: "${env.testLabel}"],
            'upload result': [closure: { uploadTestResult() }]
        ], [disablePre: true])
    },
    pre: {
        runPipeline([
            'setup pre': [closure: { setUpOnNode() }],
        ], [disablePost: true])
    }
])
