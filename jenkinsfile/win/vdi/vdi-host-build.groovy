@Library('swqa-ci')

import org.swqa.tools.git

//author: liya
//email: <EMAIL>

def initGitlabStatus() {
    env.debug = 'false'
    List names = env.initGitlabStatus.split(',').collect { "win/${it}" }
    /* groovylint-disable-next-line UnnecessarySetter */
    try { new git().setGitlabStatus(names) }catch (e) { print(e) }
}

def loadCiConfig() {
    def ciConfigPath = 'gr-kmd\\ci\\VDIConfig.yaml'
    def ciConfig = readYaml file: ciConfigPath
    echo "ciConfig: ${ciConfig}"
    return ciConfig
}

def fetchCode() {
    if (env.gitlabSourceRepoName == 'gr-kmd') {
        new git().fetchCode(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true, updateBuildDescription: true])
    }else if (env.gitlabSourceRepoName == 'm3d' || env.gitlabSourceRepoName == 'mt-video-drv' || env.gitlabSourceRepoName == 'mtkmd') {
        new git().fetchCode('gr-kmd', 'develop')
    }else {
        new git().fetchCode('gr-kmd', env.gitlabTargetBranch)
    }

    new git().updateSubmodule('gr-kmd')

    if (env.gitlabSourceRepoName == 'mt-video-drv') {
        new git().fetchCode(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true, updateBuildDescription: true])
        new git().updateSubmodule(env.gitlabSourceRepoName)
        dir('gr-kmd/mtvpu') {
            deleteDir()
        }

        sh 'mv mt-video-drv gr-kmd/mtvpu'
        sh 'ls -l gr-kmd/mtvpu'
    }

    synchronizeSubmodule('wddm', 'gr-kmd', 'shared_include')

    ciConfig = loadCiConfig()
}

def fetchwddm() {
    if (env.gitlabSourceRepoName == 'wddm') {
        new git().fetchCode(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestLastCommit, [disableSubmodules: true, updateBuildDescription: true])
    }else if (env.gitlabSourceRepoName == 'gr-kmd') {
        new git().fetchCode('wddm', env.gitlabTargetBranch, env.wddmCommitId, [preBuildMerge: false, disableSubmodules: true, shallow: false, updateBuildDescription: true])
    }else {
        new git().fetchCode('wddm', 'develop', null, [preBuildMerge: false, disableSubmodules: true, shallow: true, updateBuildDescription: true])
    }

    new git().updateSubmodule('wddm', null, 'shared_include')

    ossBranchPath = constants.genOssPath(env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestIid)
    ossAlias = constants.genOssAlias(ossBranchPath)
    prefix = constants.genOssPrefix(ossBranchPath)
    baseUrl = "${prefix}/${ossBranchPath}"
}

def fetchGuestDriver() {
    if (env.gitlabSourceRepoName == 'gr-kmd') {
        def latestVdiPkg = sh(script: "curl --insecure https://oss.mthreads.com/sw-build/VDI/${env.gitlabTargetBranch}/latest.txt", returnStdout: true).trim()
        def latestVdiPkgName = latestVdiPkg.split('/')[-1]

        env.guest_driver_url = "${latestVdiPkg}/${latestVdiPkgName}_wddm_vdi.tar.gz"
        if (env.gitlabTargetBranch.contains('release')) {
            env.guest_driver_url = "${latestVdiPkg}/${latestVdiPkgName}_wddm_vdi_release.tar.gz"
        }
        env.m3d_test_url = "${latestVdiPkg}/${latestVdiPkgName}_m3dTest.tar.gz"
        env.directstream_test_url = "${latestVdiPkg}/${latestVdiPkgName}_directstreamTest.tar.gz"
        env.wddmCommitId = latestVdiPkgName.split('_')[0]
    }
    def guest_driver_pkg_name = env.guest_driver_url.split('/')[-1]

    dir('vdi_guest') {
        sh """
            wget -q "${env.guest_driver_url}" --no-check-certificate
            tar xvzf "${guest_driver_pkg_name}" || exit 1
        """
    }
}

def build(Map buildConfig, String ossBranchPath) {
    def builds = ciConfig.builds
    def commonCmd = builds.common_cmd
    def pkgVersion = ciConfig.pkg_version

    def parallelSteps = [:]
    def ubuntuBuildDone = false

    builds.platforms.each { platform, buildList ->
        parallelSteps[platform] = {
            buildList.each { build ->
                def pkgName = build.pkgName.replace('${pkg_version}', pkgVersion)
                def dockerImage = build.dockerImage
                def cmd = adjustCmdFormat(build.cmd.replace('${common_cmd}', commonCmd).replace('${pkg_version}', pkgVersion))

                echo "Platform: ${platform}"
                echo "Package Name: ${pkgName}"
                echo "Docker Image: ${dockerImage}"
                echo "Command: ${cmd}"

                def shortPlatformName = platform.replace('NewStart_', 'N')
                dir("${shortPlatformName}") {
                    lock("vdi-lock_${env.BUILD_NUMBER}") {
                        fetchCode()
                    }

                    if (platform in ['rpm', 'deb']) {
                        // Wait for Ubuntu build to complete
                        timeout(30) {
                            while (!ubuntuBuildDone) {
                                echo 'Waiting for Ubuntu build to complete...'
                                sleep(10)
                            }
                        }
                        dkms_build(env.gitlabMergeRequestIid, dockerImage, "${cmd}")
                        retry(2) { uploadDkms(pkgName, platform, env.gitlabMergeRequestIid, ossBranchPath) }
                    } else {
                        docker.image(dockerImage).inside {
                            dir('gr-kmd') {
                                sh cmd
                            }
                        }
                        retry(2) { packagedriver(pkgName, env.gitlabSourceRepoName, env.gitlabTargetBranch, env.gitlabMergeRequestIid, ossBranchPath) }
                        if (platform == 'Ubuntu') {
                            ubuntuBuildDone = true
                        }
                    }
                }
            }
        }
    }

    try {
        new git().setGitlabStatus('win/vdi_build', 'pending')
        parallel parallelSteps
        new git().setGitlabStatus('win/vdi_build', 'success')
    } catch (e) {
        currentBuild.result = 'FAIL'
        new git().setGitlabStatus('win/vdi_build', 'failed')
    }
}

def packagedriver(String pkgName, String repo, String branch, String commitId, String ossBranchPath) {
    def buildDir = "${commitId}_${pkgName}"
    def package_name = "${commitId}_${pkgName}.tar.gz"

    oss.install()

    sh """
        mkdir -p ${buildDir}/firmware && \
        cp -rf gr-kmd/mtvpu/firmware/*.bin ${buildDir}/firmware && \
        cp -rf ${env.WORKSPACE}/vdi_guest/*.vz.* ${buildDir}/firmware && \
        cp gr-kmd/binary_*/target_x86_64/kbuild/mtgpu.ko ${buildDir} && \
        tar -cvzf ${package_name} -C ${buildDir} . && \
        mc cp ${package_name} ${ossAlias}/${ossBranchPath}/
    """

    currentBuild.description += "Driver binary: ${baseUrl}/${package_name}<br>"
}

def executeVdiTest() {
    def host_driver_url = "${baseUrl}/${env.gitlabMergeRequestIid}_kmd_Ubuntu_release.tar.gz"
    runPipeline.runJob([
        job: 'test.vdi.win',
        wait: false,
        parameters: [
            triggerInfo: env.triggerInfo,
            wddm_vdi_pkg_url: env.guest_driver_url,
            kmd_vdi_pkg_url: host_driver_url,
            m3d_download_url: env.m3d_test_url,
            directstream_download_url: env.directstream_test_url,
            branch: env.gitlabTargetBranch,
            gitlabSourceRepoName: env.gitlabSourceRepoName,
            gitlabMergeRequestLastCommit: env.gitlabMergeRequestLastCommit,
            gitlabMergeRequestIid: env.gitlabMergeRequestIid
        ]
    ])
}

def dkms_build(String commitId, String dockerImage, String cmd, String buildDir = 'Ubuntu') {
    def driverDir = "${env.WORKSPACE}/${buildDir}/${commitId}_kmd_${buildDir}_release"
    def firmwareDir = 'gr-kmd/ci_win_fw'

    sh """
        mkdir -p ${firmwareDir} && \
        cp -rf ${driverDir}/firmware/*.vz.* ${firmwareDir}
    """

    docker.image(dockerImage).inside {
        dir('gr-kmd') {
            sh cmd
        }
    }
}

def adjustCmdFormat(String cmd) {
    def envVarPattern = /(\w+=[^\s]+)/
    def envVars = []
    def commandParts = []
    def lastOption = ''

    cmd.tokenize().each { part ->
        if (part ==~ envVarPattern) {
            envVars << part
        } else if (part.startsWith('-j32')) {
            lastOption = part
        } else {
            commandParts << part
        }
    }

    def adjustedCmd = "${commandParts.join(' ')} ${envVars.join(' ')} ${lastOption}".trim()
    return adjustedCmd
}

def uploadDkms(String pkgName, String platform, String commitId, String ossBranchPath) {
    try {
        def targetDirPattern = 'gr-kmd/binary_*/target_x86_64'
        def fileName = "${pkgName}.${platform}"
        def destinationFile = "${commitId}_${fileName}"
        def ossPath = "${ossAlias}/${ossBranchPath}/${destinationFile}"

        oss.setUp()

        def targetDir = sh(script: "find gr-kmd -type d -path '${targetDirPattern}' | head -n 1", returnStdout: true).trim()
        if (targetDir) {
            dir(targetDir) {
                if (fileExists(fileName)) {
                    echo "Moving ${fileName} to ${destinationFile}"
                    sh "mv ${fileName} ${destinationFile}"

                    echo "Uploading ${destinationFile} to OSS at ${ossPath}"
                    sh "mc cp ${destinationFile} ${ossPath}"

                    def fileUrl = "${baseUrl}/${destinationFile}"
                    currentBuild.description += "Driver binary: ${fileUrl} <br>"
                    echo "File successfully uploaded: ${fileUrl}"
                } else {
                    error "File ${fileName} not found in ${targetDir}"
                }
            }
        } else {
            error "Target directory not found for pattern ${targetDirPattern}"
        }
    } catch (e) {
        echo "Error occurred during upload: ${e.message}"
        error "Upload failed: ${e}"
    }
}

def synchronizeSubmodule(String repoAPath, String repoBPath, String submodulePath) {
    if (!fileExists("${env.WORKSPACE}/${repoAPath}")) {
        error("Repository A not found at ${repoAPath}")
    }
    if (!fileExists(repoBPath)) {
        error("Repository B not found at ${repoBPath}")
    }

    def commitA = sh(script: "cd ${env.WORKSPACE}/${repoAPath}/${submodulePath} && git rev-parse HEAD", returnStdout: true).trim()
    def commitB = sh(script: "cd ${repoBPath}/${submodulePath} && git rev-parse HEAD", returnStdout: true).trim()

    echo "Commit A (${repoAPath}): ${commitA}"
    echo "Commit B (${repoBPath}): ${commitB}"

    if (commitA == commitB) {
        echo 'Submodules are already synchronized. No action required.'
        return
    }

    def resultAtoB = sh(
        script: "cd ${env.WORKSPACE}/${repoAPath}/${submodulePath} && git merge-base --is-ancestor ${commitB} ${commitA} && echo newer || echo older",
        returnStdout: true
    ).trim()

    if (resultAtoB == 'newer') {
        echo 'Repository A is newer. Synchronizing A to B.'
    // sh "rsync -av --progress ${env.WORKSPACE}/${repoAPath}/${submodulePath}/ ${repoBPath}/${submodulePath}/"
    } else if (resultAtoB == 'older') {
        echo 'Repository B is newer. Synchronizing B to A.'
    // sh "rsync -av --progress ${repoBPath}/${submodulePath}/ ${env.WORKSPACE}/${repoAPath}/${submodulePath}/"
    } else {
        echo 'Conflict detected or inconsistent states.'
        error 'Unable to determine synchronization direction due to conflicting results or detached HEADs.'
    }

    sh "ls -la ${env.WORKSPACE}/${repoAPath}/${submodulePath}"
    sh "ls -la ${repoBPath}/${submodulePath}"
}

def executeBuild(Map ciConfig, String ossBranchPath) {
    if (ciConfig?.separateBuildAndTest == 'true' && env.gitlabSourceRepoName == 'gr-kmd') {
        if (env.gitlabActionType == 'MERGE') {
            build(ciConfig, ossBranchPath)
        }

        if (env.gitlabActionType == 'NOTE') {
            if (!(env.gitlabTriggerPhrase =~ /(?i)runtest/)) {
                build(ciConfig, ossBranchPath)
            } else {
                def mrStatus = new git().getGitlabCommitStatus(env.gitlabSourceRepoName, env.gitlabMergeRequestLastCommit, 'win/vdi_build')
                if (mrStatus != 'success') {
                    build(ciConfig, ossBranchPath)
                }
            }
            if (env.runTests == 'true') {
                executeVdiTest()
            }
        }
    } else {
        build(ciConfig, ossBranchPath)
        if (env.runTests == 'true') {
            executeVdiTest()
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [:]

    workflow['initGitlabStatus'] = [closure: { initGitlabStatus() }]

    workflow.putAll([
        'fetchGuestDriver': [closure: { fetchGuestDriver() }],
        'fetchwddm': [closure: { fetchwddm() }],
        'fetchCode': [closure: { fetchCode() }],
        'build': [closure: { executeBuild(ciConfig, ossBranchPath) }]
    ])

    runPipeline(workflow, [disablePost: true])
}
