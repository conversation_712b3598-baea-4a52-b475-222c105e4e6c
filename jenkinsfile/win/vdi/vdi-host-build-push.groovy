@Library('swqa-ci')

import org.swqa.tools.git

//author: liya
//email: <EMAIL>

def loadCiConfig() {
    def ciConfigPath = 'gr-kmd\\ci\\VDIConfig.yaml'
    def ciConfig = readYaml file: ciConfigPath
    echo "ciConfig: ${ciConfig}"
    return ciConfig
}

def fetchHostCode(String branch) {
    new git().fetchCode('gr-kmd', branch)
    new git().updateSubmodule('gr-kmd')

    ciConfig = loadCiConfig()
}

def fetchGuestDriver() {
    def guest_driver_pkg_name = env.guest_driver_url.split('/')[-1]

    dir('vdi_guest') {
        sh """
            wget -q "${env.guest_driver_url}" --no-check-certificate
            tar xvzf "${guest_driver_pkg_name}" || exit 1
        """
    }
}

def build(Map buildConfig, String branch, String wddm_commitId, String kmd_commitId, String ossBranchPath) {
    def builds = ciConfig.builds
    def commonCmd = builds.common_cmd
    def pkgVersion = ciConfig.pkg_version

    def parallelSteps = [:]
    def ubuntuBuildDone = false

    builds.platforms.each { platform, buildList ->
        parallelSteps[platform] = {
            buildList.each { build ->
                def pkgName = build.pkgName.replace('${pkg_version}', pkgVersion)
                def dockerImage = build.dockerImage
                def cmd = adjustCmdFormat(build.cmd.replace('${common_cmd}', commonCmd).replace('${pkg_version}', pkgVersion))

                echo "Platform: ${platform}"
                echo "Package Name: ${pkgName}"
                echo "Docker Image: ${dockerImage}"
                echo "Command: ${cmd}"

                dir("${platform}") {
                    lock("vdi-lock_${env.BUILD_NUMBER}") {
                        fetchHostCode(branch)
                    }

                    if (platform in ['rpm', 'deb']) {
                        // Wait for Ubuntu build to complete
                        timeout(30) {
                            while (!ubuntuBuildDone) {
                                echo 'Waiting for Ubuntu build to complete...'
                                sleep(10)
                            }
                        }
                        dkms_build(wddm_commitId, kmd_commitId, dockerImage, "${cmd}")
                        retry(2) { uploadDkms(pkgName, platform, wddm_commitId, kmd_commitId, ossBranchPath) }
                    } else {
                        docker.image(dockerImage).inside {
                            dir('gr-kmd') {
                                sh cmd
                            }
                        }
                        retry(2) { packagedriver(pkgName, branch, wddm_commitId, kmd_commitId, ossBranchPath) }
                        if (platform == 'Ubuntu') {
                            ubuntuBuildDone = true
                        }
                    }
                }
            }
        }
    }

    parallel parallelSteps
}

def dkms_build(String wddm_commitId, String kmd_commitId, String dockerImage, String cmd, String buildDir = 'Ubuntu') {
    def driverDir = "${env.WORKSPACE}/${buildDir}/${wddm_commitId}_${kmd_commitId}_kmd_${buildDir}_release"
    def firmwareDir = 'gr-kmd/ci_win_fw'

    sh """
        mkdir -p ${firmwareDir} && \
        cp -rf ${driverDir}/firmware/*.vz.* ${firmwareDir}
    """

    docker.image(dockerImage).inside {
        dir('gr-kmd') {
            sh cmd
        }
    }
}

def packagedriver(String pkgName, String branch, String wddm_commitId, String kmd_commitId, String ossBranchPath) {
    def buildDir = "${wddm_commitId}_${kmd_commitId}_${pkgName}"
    def package_name = "${wddm_commitId}_${kmd_commitId}_${pkgName}.tar.gz"

    oss.setUp()

    sh """
        mkdir -p ${buildDir}/firmware && \
        cp -rf gr-kmd/mtvpu/firmware/*.bin ${buildDir}/firmware && \
        cp -rf gr-kmd/dkms/dkms.post_install.vz ${buildDir} && \
        cp -rf ${env.WORKSPACE}/vdi_guest/*.vz.* ${buildDir}/firmware && \
        cp gr-kmd/binary_*/target_x86_64/kbuild/mtgpu.ko ${buildDir} && \
        tar -cvzf ${package_name} -C ${buildDir} . && \
        mc cp ${package_name} oss/${ossBranchPath}/
    """

    currentBuild.description += "Driver binary: https://oss.mthreads.com/${ossBranchPath}/${package_name}<br>"
}

def adjustCmdFormat(String cmd) {
    def envVarPattern = /(\w+=[^\s]+)/
    def envVars = []
    def commandParts = []
    def lastOption = ''

    cmd.tokenize().each { part ->
        if (part ==~ envVarPattern) {
            envVars << part
        } else if (part.startsWith('-j32')) {
            lastOption = part
        } else {
            commandParts << part
        }
    }

    def adjustedCmd = "${commandParts.join(' ')} ${envVars.join(' ')} ${lastOption}".trim()
    return adjustedCmd
}

def uploadDkms(String pkgName, String platform, String wddm_commitId, String kmd_commitId, String ossBranchPath) {
    try {
        def targetDirPattern = 'gr-kmd/binary_*/target_x86_64'
        def fileName = "${pkgName}.${platform}"
        def destinationFile = "${wddm_commitId}_${kmd_commitId}_${fileName}"
        def ossPath = "oss/${ossBranchPath}/${destinationFile}"

        oss.setUp()

        def targetDir = sh(script: "find gr-kmd -type d -path '${targetDirPattern}' | head -n 1", returnStdout: true).trim()
        if (targetDir) {
            dir(targetDir) {
                if (fileExists(fileName)) {
                    echo "Moving ${fileName} to ${destinationFile}"
                    sh "mv ${fileName} ${destinationFile}"

                    echo "Uploading ${destinationFile} to OSS at ${ossPath}"
                    sh "mc cp ${destinationFile} ${ossPath}"

                    def fileUrl = "https://oss.mthreads.com/${ossBranchPath}/${destinationFile}"
                    currentBuild.description += "Driver binary: ${fileUrl} <br>"
                    echo "File successfully uploaded: ${fileUrl}"
                } else {
                    error "File ${fileName} not found in ${targetDir}"
                }
            }
        } else {
            error "Target directory not found for pattern ${targetDirPattern}"
        }
    } catch (e) {
        echo "Error occurred during upload: ${e.message}"
        error "Upload failed: ${e}"
    }
}

def getRenamedFileName(String originalFileName, String branchName) {
    return originalFileName.replaceFirst(/^.*?_/, branchName + '_')
}

def rename_guest_file(String ossBranchPath) {
    def ossBranchName = ossBranchPath.split('/')[-1]

    def fileMap = [
        'guest_driver': env.guest_driver_url,
        'm3d_test': env.m3d_test_url,
        'directstream_test': env.directstream_test_url
    ]

    dir('vdi_guest') {
        fileMap.each { key, url ->
            if (key != 'guest_driver') {
                sh "wget -q \"${url}\" --no-check-certificate"
            }
        }

        oss.setUp()

        fileMap.each { key, url ->
            def originalFileName = url.split('/')[-1]
            def renamedFileName = getRenamedFileName(originalFileName, ossBranchName)

            sh "mv ${originalFileName} ${renamedFileName}"
            sh "mc cp ${renamedFileName} oss/${ossBranchPath}/"
        }
    }
}

def uploadCommitInfo(branch, wddmCommitId, kmdCommitId) {
    def ossBranchPath = constants.genOssPath('VDI', branch)
    def ossCommitPath = constants.genOssPath('VDI', branch, wddmCommitId) + "_${kmdCommitId}"
    winBuild.updateLatestTxt(ossCommitPath)
    try {
        sh """
            mc find oss/${ossBranchPath}/history.txt
            mc cp oss/${ossBranchPath}/history.txt history.txt
            sed -i "1i wddmCommitId:${wddmCommitId}_kmdCommitId:${kmdCommitId} `date '+%Y-%m-%d %H:%M:%S'`" history.txt
        """
    } catch (e) {
        sh """
            echo "wddmCommitId:${wddmCommitId}_kmdCommitId:${kmdCommitId} `date '+%Y-%m-%d %H:%M:%S'`" > history.txt
        """
    } finally {
        sh "mc cp history.txt oss/${ossBranchPath}/"
    }
}

runner.start(env.runChoice) {
    // sh 'sudo chown -R jenkins:jenkins .'
    deleteDir()
    def wddm_commitId = env.guest_driver_url.split('/')[-2]
    def branch = env.guest_driver_url.split('/')[-3]
    fetchHostCode(branch)
    def kmd_commitId = new git().getCurrentCommitID('gr-kmd')
    def ossBranchPath = constants.genOssPath('VDI', branch, wddm_commitId) + "_${kmd_commitId}"
    def workflow = [
        'fetchGuestDriver': [closure: { fetchGuestDriver() }],

        'build': [closure: { build(ciConfig, branch, wddm_commitId, kmd_commitId, ossBranchPath) }],

        'renameGuestFile': [closure: { rename_guest_file(ossBranchPath) }],

        'updateLatestTxt': [closure: { uploadCommitInfo(branch, wddm_commitId, kmd_commitId) }]
    ]

    runPipeline(workflow, [disablePost:true])
}
