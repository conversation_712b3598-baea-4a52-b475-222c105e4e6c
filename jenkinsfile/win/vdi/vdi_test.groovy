@Library('swqa-ci')

import org.swqa.tools.common
import org.swqa.tools.git

common_libs = new common()

bmc = null

def vdi_test() {
    try {
        bmc = getBmcInfo(env.NODE_NAME)
        println "[vdi_test] BMC info: ${bmc}"
    } catch (ex) {
        println "[vdi_test] Failed to get BMC info: ${ex}"
    }

    if (!bmc?.ip || !bmc?.credId) {
        error "[vdi_test] Missing BMC info for node ${env.NODE_NAME}. Cannot proceed with the test."
    }

    def image_path = '/root/mt_ci/qemu'

    def gpu_id_map = [
        'CI_GFX_S3000_Ubuntu_Linux_test_24.37': ['gpu_id': '3b', 'test_node_guest': 'win10_CI_vdi_ubuntu_Test_01'],
        'CI_GFX_S3000_Ubuntu_Linux_test_24.35': ['gpu_id': '3b', 'test_node_guest': 'win10_CI_vdi_ubuntu_Test_02'],
        'CI_GFX_S3000_Ubuntu_Linux_test_103.186': ['gpu_id': 'ca', 'test_node_guest': 'win10_CI_vdi_ubuntu_Test_03']
    ]

    if (branch.contains('release')) {
        env.d3dTestCommitId = '38fb102dd'
    }
    if (branch.contains('release_vgpu_2.5.6')) {
        env.d3dTestCommitId = '20240101_master'
    }

    cleanWs()
    currentBuild.description = "<br>build node: ${env.NODE_NAME} <br>"
    currentBuild.description += "${env.gitlabSourceRepoName}_PR${env.gitlabMergeRequestIid}<br>"

    node('Linux_jump') { new git().setGitlabStatus('win/vdi_test', 'pending') }
    try {
        stage('mtgpu status check') {
            sh'''
                sudo rmmod mtgpu 2>log.txt || true
            '''
            if (fileExists('log.txt')) {
                try {
                    sh(script: "grep 'Module mtgpu is not currently loaded' log.txt", returnStdout: true).trim()
                }catch (e) {
                    reboot(20)
                }
            }
        }

        stage('fetch guest driver') {
            dir('wddm-vdi') {
                retry(10) {
                    timeout(3) {
                        sh """
                            sleep 20
                            wget -q --no-check-certificate ${wddm_vdi_pkg_url} && tar xzf *.tar.gz
                        """
                    }
                }
            }
        }

        stage('fetch host mtgpu.ko') {
            def kmd_pkg_name = env.kmd_vdi_pkg_url.split('/')[-1]
            install_pkg_name = kmd_pkg_name.split('\\.')[0]
            dir("${install_pkg_name}") {
                sh"""
                    wget -q --no-check-certificate ${env.kmd_vdi_pkg_url}  -O ${kmd_pkg_name}
                    tar xvzf ${kmd_pkg_name}
                """
            }
        }

        dir("${install_pkg_name}") {
            stage('install kmd') {
                sh '''
                    modprobe drm_kms_helper
                    modprobe vfio
                    modprobe vfio_mdev
                    modprobe gpu-sched
                '''

                sh 'cp -rf ./firmware/*.bin /usr/lib/firmware/mthreads/'
                sh 'cp -rf ../wddm-vdi/*.vz.* /usr/lib/firmware/mthreads/'

                if (branch != 'develop' || branch != 'release_vgpu_2.7.0' || branch != 'release_vgpu_2.7.5') {
                    sh"""
                        insmod mtgpu.ko
                        dmesg | grep 'MooreThreads GPU drm driver loaded successfully' || exit 1
                    """
                }else {
                    sh"""
                        insmod mtgpu.ko mtgpu_driver_mode=0 display=dummy mtgpu_load_windows_firmware=1
                        dmesg | grep 'MooreThreads GPU drm driver loaded successfully' || exit 1
                    """
                }
            }
        }
        def img_list = ['win10_vdi_test_01']

        dir(image_path) {
            img_list.each { img_name ->
                sh "qemu-img create -F qcow2 -f qcow2 -b ${img_name}_base.img ${img_name}.img"
            }
        }

        def test_parallel = [:]
        for (int guest_number = 1; guest_number < 2; guest_number++) {
            def number = guest_number
            def gpu_id_data = gpu_id_map.get(env.NODE_NAME, [:])
            def gpu_id = gpu_id_data.gpu_id ?: ''
            def test_nodes_guest = gpu_id_data.test_node_guest ?: ''
            if (guest_number < 10) {
                number = "0${guest_number}"
            }
            def type_name = "Test${number}"
            test_parallel[type_name] = {
                // test for 1101
                timeout(10) {
                    stage('create vGPU and setup VM') {
                        sh """
                            sudo mdevctl start -u c4f702ca-c69d-4d7d-a526-5fdcf78d34${number} -p 0000:${gpu_id}:00.0 -t mtgpu-1101
                            sleep 10
                            virsh create ${image_path}/vdi_${number}.xml
                            sleep 60
                        """
                    }
                }

                node(test_nodes_guest) {
                    cleanWs(deleteDirs: true, patterns: [[pattern: 'd3dtests/', type: 'EXCLUDE']])
                    try {
                        timeout(3) {
                            stage('install guest driver') {
                                def inf_name = 'MT-VGPU-ENCODE'
                                if (branch == 'master' || branch == 'develop') {
                                    inf_name = 'MT-VGPU-FW-ENCODE'
                                }else if (branch == 'release_vgpu_2.7.0' || branch == 'release_vgpu_2.7.5') {
                                    inf_name = 'MT-VGPU-FW-ENCODE-REL'
                                }

                                retry(2) {
                                    dir('fre_win10_amd64') {
                                        script {
                                            def downloadSuccess = false
                                            retry(3) {
                                                sleep 20
                                                def result = sh(
                                                    script: "wget -q ${wddm_vdi_pkg_url} && tar xzf *.tar.gz",
                                                    returnStatus: true
                                                )
                                                downloadSuccess = (result == 0)
                                                if (!downloadSuccess) {
                                                    error 'Driver download failed'
                                                }
                                            }

                                            if (downloadSuccess && fileExists("${inf_name}.inf")) {
                                                sleep 20
                                                bat "devcon update ${inf_name}.inf \"PCI\\VEN_1ED5\""
                                                sleep 20
                                            } else {
                                                error 'Driver files not found or download failed'
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        stage('Get the MTvGPU model') {
                            def mtvGPU_model = sh(script: 'C:\\\\Windows\\\\System32\\\\pnputil.exe -enum-devices -connected -class Display | grep -i -a "mtvgpu"', returnStdout: true).trim()
                            echo mtvGPU_model
                            def extractedValue = (mtvGPU_model =~ /MTvGPU-(\d+)/)?.getAt(0)?.getAt(1)
                            echo "Extracted value: ${extractedValue}"
                            encodeNumber = extractedValue[0]
                            resolution = extractedValue[1]
                        }

                        def directstream_test_pool = [:]
                        List<Integer> encodeNumbers = []
                        (1..encodeNumber.toInteger()).each { encode_number ->
                            encodeNumbers << encode_number
                        }
                        encodeNumbers.each { encode_number ->
                            directstream_test_pool["encode-${encode_number}"] = {
                                dir("encode-${encode_number}") {
                                    stage('fetch DirectStream test') {
                                        def directstream_pkg_name = env.directstream_download_url.split('/')[-1]
                                        bat"""
                                            wget -q ${env.directstream_download_url} -O ${directstream_pkg_name}
                                            tar xvzf ${directstream_pkg_name}
                                        """
                                    }

                                    dir('test') {
                                        bat"""
                                            enc_ci.sh -s ${resolution} -t vdi
                                            cat log.txt
                                        """
                                        result = sh(script: 'cat result.txt', returnStdout: true).trim()
                                        if (result.toInteger() == 1) {
                                            error 'test fail!'
                                        }
                                    }
                                }
                            }
                        }
                        stage('MTEncode Test') {
                            parallel directstream_test_pool
                        }

                        stage('vgpu 1101 Test') {
                            try {
                                def dxva_test_dir = "${env.WORKSPACE}\\dxvascripts"
                                fetchTestSource()
                                dir(dxva_test_dir) {
                                    bat 'wget -q --no-check-certificate https://oss-swci-sh.mthreads.com/dependency/swci/dxva/runVm1101Test.bat -O runVm1101Test.bat'
                                    bat 'runVm1101Test.bat'
                                    checkResult()
                                }
                            } catch (e) {
                                currentBuild.result = 'FAIL'
                                error("dxva Test failed: ${e}")
                            }
                        }
                    }
                    catch (e) {
                        print("Test Exception: ${e}")
                        currentBuild.result = 'FAIL'
                        throw e
                    }
                }//node

                timeout(10) {
                    stage('destroy vGPU and VM') {
                        sh '''
                            virsh list | awk 'NR>2 {print $2}'|xargs -n1 virsh destroy
                            sleep 30
                            sudo mdevctl list|awk \'{print $1}\'|xargs -n1 mdevctl stop -u
                        '''
                    }
                }

                // test for 1108
                timeout(10) {
                    stage('create vGPU and setup VM') {
                        sh """
                            sudo mdevctl start -u c4f702ca-c69d-4d7d-a526-5fdcf78d34${number} -p 0000:${gpu_id}:00.0 -t mtgpu-1108
                            sleep 10
                            virsh create ${image_path}/vdi_${number}.xml
                            mdevctl list
                            sleep 60
                        """
                    }
                }

                node(test_nodes_guest) {
                    cleanWs(deleteDirs: true, patterns: [[pattern: 'd3dtests/', type: 'EXCLUDE']])
                    try {
                        timeout(3) {
                            stage('install guest driver') {
                                def inf_name = 'MT-VGPU-ENCODE'
                                if (branch == 'master' || branch == 'develop') {
                                    inf_name = 'MT-VGPU-FW-ENCODE'
                                }else if (branch == 'release_vgpu_2.7.0' || branch == 'release_vgpu_2.7.5') {
                                    inf_name = 'MT-VGPU-FW-ENCODE-REL'
                                }

                                retry(2) {
                                    dir('fre_win10_amd64') {
                                        script {
                                            def downloadSuccess = false
                                            retry(3) {
                                                sleep 20
                                                def result = sh(
                                                    script: "wget -q ${wddm_vdi_pkg_url} && tar xzf *.tar.gz",
                                                    returnStatus: true
                                                )
                                                downloadSuccess = (result == 0)
                                                if (!downloadSuccess) {
                                                    error 'Driver download failed'
                                                }
                                            }

                                            if (downloadSuccess && fileExists("${inf_name}.inf")) {
                                                sleep 20
                                                bat "devcon update ${inf_name}.inf \"PCI\\VEN_1ED5\""
                                                sleep 20
                                            } else {
                                                error 'Driver files not found or download failed'
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        stage('Get the MTvGPU model') {
                            def mtvGPU_model = sh(script: 'C:\\\\Windows\\\\System32\\\\pnputil.exe -enum-devices -connected -class Display | grep -i -a "mtvgpu"', returnStdout: true).trim()
                            echo mtvGPU_model
                            def extractedValue = (mtvGPU_model =~ /MTvGPU-(\d+)/)?.getAt(0)?.getAt(1)
                            echo "Extracted value: ${extractedValue}"
                            encodeNumber = extractedValue[0]
                            resolution = extractedValue[1]
                        }

                        stage('vgpu 1108 Test') {
                            try {
                                def dxva_test_dir = "${env.WORKSPACE}\\dxvascripts"
                                fetchTestSource()
                                dir(dxva_test_dir) {
                                    bat 'wget -q https://oss-swci-sh.mthreads.com/dependency/swci/dxva/runVm1108Test.bat -O runVm1108Test.bat'
                                    bat 'runVm1108Test.bat'
                                    checkResult()
                                }
                            } catch (e) {
                                currentBuild.result = 'FAIL'
                                error("dxva Test failed: ${e}")
                            }
                        }
                    }
                    catch (e) {
                        print("Test Exception: ${e}")
                        currentBuild.result = 'FAIL'
                        throw e
                    }
                }//node
            }
        }
        timeout(120) {
            retry(1) { parallel test_parallel }
            node('Linux_jump') { new git().setGitlabStatus('win/vdi_test', 'success') }
        }
    }//try
    catch (e) {
        node('Linux_jump') { new git().setGitlabStatus('win/vdi_test', 'failed') }
        print("Test Exception: ${e}")
        currentBuild.result = 'FAIL'
        sh 'dmesg'
        throw e
    }
    finally {
        sh"""
            virsh destroy VDI-CI-1
            sleep 30
            rm -rf ${image_path}/win10_vdi_test_01.img
            rm -rf /var/crash/*
        """
        timeout(1) {
            sh'''
                sudo pkill -f "/dev/dri/" || true
                sudo rmmod mtgpu 2>log.txt || true
            '''
        }
    }
}

def checkResult() {
    def out = bat(script:'cat All_ResultFail.txt', returnStdout: true).trim()
    println '--------------'
    println out
    println out.length()
    if ( out.contains('H264') || out.contains('H265') || out.contains('vp9') || out.contains('av1') || out.contains('vc1')) {
        println 'some cases failed-----'
        print(out)
        error('error,please check ')
    }else {
        println 'Test passed'
    }
}

def fetchTestSource() {
    def codecs = [
        [name: 'VC1', codec: 'VC1', textFiles: ['VC1ConformanceTestList.txt'], zipFile: 'VC1.zip'],
        [name: 'H264', codec: 'H264', textFiles: ['StrmListLongSliceMode1.txt'], zipFile: 'H264.zip'],
        [name: 'H265', codec: 'H265', textFiles: ['HevcConfromanceTestListSuccessed.txt', 'Hevc10bWindows.txt'], zipFile: 'H265.zip'],
        [name: 'VP9', codec: 'VP9', textFiles: ['VP9ConformanceTestList.txt'], zipFile: 'VP9.zip'],
        [name: 'VP9_10BIT', codec: 'VP9_10bit', textFiles: ['VP910bitConformanceTestList.txt'], zipFile: 'VP910bit.zip'],
        [name: 'AV1_8BIT', codec: 'AV1_8bit', textFiles: ['av1_conformanceTestList8bit.txt'], zipFile: 'av18bit.zip'],
        [name: 'AV1_10BIT', codec: 'AV1_10bit', textFiles: ['av1_conformanceTestList10bit.txt'], zipFile: 'av110bit.zip']
    ]
    def decode_resources = 'https://swci-oss.mthreads.com/dependency/swci/dxva/'

    winTest.fetchTestRepo('dxvascripts', 'master')
    dir('dxvascripts') {
        bat """
            wget -q ${decode_resources}ffmpeg-av1.zip -O ffmpeg-av1.zip --no-check-certificate
            unzip -o ffmpeg-av1.zip
            cd exe
            unzip ffmpeg.zip
        """
    }

    codecs.each { codec ->
        winTest.prepareTestEnvironment(codec, decode_resources)
    }
}

def getBmcInfo(nodeName) {
    def parts = nodeName.tokenize('_')
    def ip = parts ? parts[-1] : ''
    ip = ip.replaceAll('-', '.')
    def bmcMap = [
        '24.35': [ip: '*************', credId: 'vdi_ci'],
        '24.37': [ip: '*************', credId: 'vdi_ci'],
        '103.186': [ip: '*************', credId: 'vdi_ci']
    ]
    def fullIp = ip
    def last2 = ip.tokenize('.').size() == 4 ? ip.tokenize('.')[-2..-1].join('.') : ip
    def result = bmcMap[fullIp] ?: bmcMap[last2] ?: [ip: '', credId: '']
    println "[getBmcInfo] nodeName=${nodeName}, ip=${ip}, fullIp=${fullIp}, last2=${last2}, result=${result}"
    return result
}

def reboothost() {
    stage('reboot host') {
        try {
            reboot(20)
        } catch (e) {
            node('Linux_jump') {
                if (bmc?.ip && bmc?.credId) {
                    withCredentials([usernamePassword(credentialsId: bmc.credId, usernameVariable: 'BMC_USER', passwordVariable: 'BMC_PASS')]) {
                        sh """
                            echo "Graceful reboot failed, using BMC reset..."
                            ipmitool -I lanplus -H ${bmc.ip} -U ${BMC_USER} -P ${BMC_PASS} power reset
                        """
                    }
                } else {
                    echo "No BMC info found for node ${env.NODE_NAME}, cannot perform BMC reset."
                }
                print("Reboot triggered, connection might be interrupted: ${e}")
            }
        }
    }
}

def reboot(int time=5) {
    timeout(time) {
        sh 'shutdown -r -t 2'
        sleep 60
        retry(12) {
            sleep 20
            echo 'retry ------'
            if (fileExists('/home')) {
                echo 'reboot ok'
            }
        }
    }
}

runner.start(env.runChoice) {
    def workflow = [
        'vdi test': [closure: { vdi_test() }]
    ]

    runPipeline(workflow, [post: { reboothost() }])
}
